import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/ramadan_model.dart';
import '../services/ramadan_service.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/enhanced_card.dart';
import '../widgets/enhanced_animations.dart';
import '../widgets/islamic_pattern.dart';

/// شاشة التسبيحات الرمضانية
class RamadanTasbihsScreen extends StatefulWidget {
  const RamadanTasbihsScreen({super.key});

  @override
  State<RamadanTasbihsScreen> createState() => _RamadanTasbihsScreenState();
}

class _RamadanTasbihsScreenState extends State<RamadanTasbihsScreen>
    with TickerProviderStateMixin {
  final RamadanService _ramadanService = RamadanService();
  late List<RamadanTasbih> _tasbihs;
  String _selectedCategory = 'all';
  final Map<String, int> _counters = {};
  late AnimationController _pulseController;

  @override
  void initState() {
    super.initState();
    _tasbihs = _ramadanService.getRamadanTasbihs();

    // تهيئة العدادات
    for (var tasbih in _tasbihs) {
      _counters[tasbih.id] = 0;
    }

    // إعداد الرسوم المتحركة
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filteredTasbihs = _getFilteredTasbihs();

    // استخدام ألوان السمة مع الحفاظ على الطابع الرمضاني
    final primaryColor = theme.colorScheme.primary;
    final ramadanBackground =
        Color.lerp(primaryColor, RamadanColors.emerald, 0.2)!;
    final ramadanPattern =
        Color.lerp(primaryColor, RamadanColors.darkGreen, 0.3)!;

    return Scaffold(
      appBar: const CustomAppBar(title: 'التسبيحات الرمضانية'),
      body: Stack(
        children: [
          // خلفية رمضانية مع ألوان السمة
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    ramadanBackground.withValues(alpha: 0.1),
                    theme.scaffoldBackgroundColor,
                  ],
                ),
              ),
            ),
          ),

          // نمط إسلامي
          Positioned.fill(
            child: Opacity(
              opacity: 0.03,
              child: IslamicPattern(color: ramadanPattern),
            ),
          ),

          // المحتوى
          Column(
            children: [
              // شريط التصفية
              _buildCategoryFilter(),

              // قائمة التسبيحات
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredTasbihs.length,
                  itemBuilder: (context, index) {
                    return FadeInAnimation(
                      delay: Duration(milliseconds: index * 100),
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: _buildTasbihCard(filteredTasbihs[index]),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _resetAllCounters,
        backgroundColor: RamadanColors.darkGreen,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.refresh),
        label: const Text('إعادة تعيين الكل'),
      ),
    );
  }

  /// بناء شريط تصفية الفئات
  Widget _buildCategoryFilter() {
    final categories = [
      {'id': 'all', 'name': 'الكل'},
      {'id': 'afterIftar', 'name': 'بعد الإفطار'},
      {'id': 'tarawih', 'name': 'التراويح'},
      {'id': 'itikaf', 'name': 'الاعتكاف'},
      {'id': 'general', 'name': 'عامة'},
    ];

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = _selectedCategory == category['id'];

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(category['name']!),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category['id']!;
                });
                HapticFeedback.lightImpact();
              },
              selectedColor: RamadanColors.darkGreen.withValues(alpha: 0.3),
              checkmarkColor: RamadanColors.gold,
            ),
          );
        },
      ),
    );
  }

  /// بناء بطاقة التسبيحة
  Widget _buildTasbihCard(RamadanTasbih tasbih) {
    final currentCount = _counters[tasbih.id] ?? 0;
    final isCompleted = currentCount >= tasbih.recommendedCount;
    final progress = currentCount / tasbih.recommendedCount;

    return EnhancedCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              tasbih.color.withValues(alpha: isCompleted ? 0.2 : 0.1),
              Colors.transparent,
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: tasbih.color.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(tasbih.icon, color: tasbih.color, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tasbih.title,
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: tasbih.color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'العدد المستحب: ${tasbih.recommendedCount}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                if (isCompleted)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: RamadanColors.gold,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'مكتمل',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // النص العربي
            GestureDetector(
              onTap: () => _showTasbihDetails(tasbih),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: tasbih.color.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  tasbih.arabicText,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    height: 1.8,
                    fontFamily: 'Uthmani',
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection.rtl,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // الترجمة
            Text(
              tasbih.translation,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: progress.clamp(0.0, 1.0),
                    backgroundColor: tasbih.color.withValues(alpha: 0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(tasbih.color),
                    minHeight: 6,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '$currentCount/${tasbih.recommendedCount}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: tasbih.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // أزرار التحكم
            Row(
              children: [
                // زر التسبيح
                Expanded(
                  flex: 2,
                  child: ElevatedButton.icon(
                    onPressed: () => _incrementCounter(tasbih),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: tasbih.color,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    icon: const Icon(Icons.add),
                    label: const Text('سبح'),
                  ),
                ),
                const SizedBox(width: 8),

                // زر إعادة التعيين
                IconButton(
                  onPressed: () => _resetCounter(tasbih.id),
                  icon: const Icon(Icons.refresh),
                  style: IconButton.styleFrom(
                    backgroundColor: tasbih.color.withValues(alpha: 0.2),
                    foregroundColor: tasbih.color,
                  ),
                ),
                const SizedBox(width: 8),

                // زر المشاركة
                IconButton(
                  onPressed: () => _shareTasbih(tasbih),
                  icon: const Icon(Icons.share),
                  style: IconButton.styleFrom(
                    backgroundColor: tasbih.color.withValues(alpha: 0.2),
                    foregroundColor: tasbih.color,
                  ),
                ),
              ],
            ),

            if (tasbih.benefits != null) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: tasbih.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: tasbih.color,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        tasbih.benefits!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: tasbih.color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// زيادة العداد
  void _incrementCounter(RamadanTasbih tasbih) {
    setState(() {
      _counters[tasbih.id] = (_counters[tasbih.id] ?? 0) + 1;
    });

    // تأثير اهتزاز
    HapticFeedback.lightImpact();

    // تأثير نبضة
    _pulseController.forward().then((_) => _pulseController.reverse());

    // تحقق من الإكمال
    if (_counters[tasbih.id] == tasbih.recommendedCount) {
      _showCompletionDialog(tasbih);
    }
  }

  /// إعادة تعيين عداد واحد
  void _resetCounter(String tasbihId) {
    setState(() {
      _counters[tasbihId] = 0;
    });
    HapticFeedback.mediumImpact();
  }

  /// إعادة تعيين جميع العدادات
  void _resetAllCounters() {
    setState(() {
      for (var key in _counters.keys) {
        _counters[key] = 0;
      }
    });
    HapticFeedback.heavyImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إعادة تعيين جميع العدادات')),
    );
  }

  /// عرض تفاصيل التسبيحة
  void _showTasbihDetails(RamadanTasbih tasbih) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              tasbih.title,
              textAlign: TextAlign.center,
              style: TextStyle(color: tasbih.color),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // النص العربي
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: tasbih.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      tasbih.arabicText,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        height: 1.8,
                        fontFamily: 'Uthmani',
                      ),
                      textAlign: TextAlign.center,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // النطق
                  Text(
                    tasbih.transliteration,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),

                  // الترجمة
                  Text(
                    tasbih.translation,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),

                  // العدد المستحب
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: tasbih.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'العدد المستحب: ${tasbih.recommendedCount}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: tasbih.color,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  if (tasbih.source != null) ...[
                    const SizedBox(height: 12),
                    Text(
                      'المصدر: ${tasbih.source}',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: tasbih.color),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: tasbih.arabicText));
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم نسخ التسبيحة')),
                  );
                },
                icon: const Icon(Icons.copy),
                label: const Text('نسخ'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: tasbih.color,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الإكمال
  void _showCompletionDialog(RamadanTasbih tasbih) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(RamadanIcons.star, color: RamadanColors.gold, size: 28),
                const SizedBox(width: 8),
                const Text('بارك الله فيك!'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'لقد أكملت تسبيحة "${tasbih.title}"',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: RamadanColors.gold.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'تقبل الله منك صالح الأعمال',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: RamadanColors.gold,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RamadanColors.gold,
                  foregroundColor: Colors.white,
                ),
                child: const Text('الحمد لله'),
              ),
            ],
          ),
    );

    // تأثير اهتزاز للإكمال
    HapticFeedback.heavyImpact();
  }

  /// مشاركة التسبيحة
  void _shareTasbih(RamadanTasbih tasbih) {
    // يمكن تنفيذ مشاركة التسبيحة هنا
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('مشاركة "${tasbih.title}""')));
  }

  /// تصفية التسبيحات حسب الفئة المختارة
  List<RamadanTasbih> _getFilteredTasbihs() {
    if (_selectedCategory == 'all') {
      return _tasbihs;
    }
    return _tasbihs
        .where((tasbih) => tasbih.category == _selectedCategory)
        .toList();
  }
}
