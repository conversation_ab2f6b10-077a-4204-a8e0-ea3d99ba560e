import 'package:flutter/material.dart';
import '../models/achievement_model.dart';

/// ويدجت عرض الإنجاز
class AchievementWidget extends StatelessWidget {
  final Achievement achievement;
  final bool isUnlocked;

  const AchievementWidget({
    super.key,
    required this.achievement,
    required this.isUnlocked,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isUnlocked ? 6 : 2,
      color: theme.cardColor,
      shadowColor: isDarkMode
          ? Colors.black.withAlpha(40)
          : Colors.black.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isUnlocked
              ? achievement.color.withAlpha(128)
              : (isDarkMode
                  ? const Color(0x4D9E9E9E)
                  : Colors.grey.withAlpha(30)),
          width: isUnlocked ? 2 : 1,
        ),
      ),
      child: Stack(
        children: [
          // تأثير بصري للإنجازات المفتوحة
          if (isUnlocked)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      achievement.color.withAlpha(13),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),

          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // أيقونة الإنجاز
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: isUnlocked
                        ? achievement.color.withAlpha(26)
                        : Colors.grey.withAlpha(26),
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: isUnlocked
                          ? achievement.color.withAlpha(128)
                          : Colors.grey.withAlpha(128),
                      width: 2,
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Icon(
                        achievement.icon,
                        size: 28,
                        color: isUnlocked
                            ? achievement.color
                            : Colors.grey,
                      ),
                      
                      // تأثير التألق للإنجازات المفتوحة
                      if (isUnlocked)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30),
                              boxShadow: [
                                BoxShadow(
                                  color: achievement.color.withAlpha(64),
                                  blurRadius: 8,
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // معلومات الإنجاز
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // العنوان ومستوى الإنجاز
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              achievement.title,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isUnlocked
                                    ? achievement.color
                                    : theme.colorScheme.onSurface,
                              ),
                            ),
                          ),
                          _buildLevelBadge(theme),
                        ],
                      ),

                      const SizedBox(height: 4),

                      // الوصف
                      Text(
                        achievement.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withAlpha(179),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 8),

                      // شريط التقدم والنقاط
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // شريط التقدم
                                LinearProgressIndicator(
                                  value: achievement.progress,
                                  backgroundColor: Colors.grey.withAlpha(51),
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    isUnlocked
                                        ? achievement.color
                                        : Colors.grey,
                                  ),
                                  minHeight: 6,
                                ),
                                const SizedBox(height: 4),
                                
                                // نص التقدم
                                Text(
                                  '${achievement.currentValue}/${achievement.targetValue}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: theme.colorScheme.onSurface.withAlpha(179),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(width: 16),

                          // النقاط
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: isUnlocked
                                  ? achievement.color.withAlpha(26)
                                  : Colors.grey.withAlpha(26),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.stars,
                                  size: 14,
                                  color: isUnlocked
                                      ? achievement.color
                                      : Colors.grey,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${achievement.points}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: isUnlocked
                                        ? achievement.color
                                        : Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      // تاريخ الفتح (للإنجازات المفتوحة)
                      if (isUnlocked && achievement.unlockedDate != null) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              size: 14,
                              color: achievement.color,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'تم الفتح في ${_formatDate(achievement.unlockedDate!)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: achievement.color,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          // أيقونة القفل للإنجازات المقفلة
          if (!isUnlocked)
            Positioned(
              top: 8,
              right: 8,
              child: Icon(
                Icons.lock,
                size: 16,
                color: Colors.grey,
              ),
            ),

          // أيقونة النجمة للإنجازات المفتوحة
          if (isUnlocked)
            Positioned(
              top: 8,
              right: 8,
              child: Icon(
                Icons.star,
                size: 16,
                color: achievement.color,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء شارة المستوى
  Widget _buildLevelBadge(ThemeData theme) {
    final levelColor = PredefinedAchievements.getLevelColor(achievement.level);
    final levelName = PredefinedAchievements.getLevelName(achievement.level);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: levelColor.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: levelColor.withAlpha(128),
          width: 1,
        ),
      ),
      child: Text(
        levelName,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: levelColor,
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
