import 'package:flutter/material.dart';

/// أنواع الإنجازات
enum AchievementType {
  streak, // سلسلة أيام متتالية
  daily, // إنجازات يومية
  weekly, // إنجازات أسبوعية
  monthly, // إنجازات شهرية
  total, // إنجازات إجمالية
  special, // إنجازات خاصة
}

/// مستويات الإنجازات
enum AchievementLevel {
  bronze, // برونزي
  silver, // فضي
  gold, // ذهبي
  platinum, // بلاتيني
  diamond, // ماسي
}

/// نموذج بيانات الإنجاز
class Achievement {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final AchievementType type;
  final AchievementLevel level;
  final int targetValue;
  final int currentValue;
  final int points;
  final bool isUnlocked;
  final DateTime? unlockedDate;
  final Color color;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.type,
    required this.level,
    required this.targetValue,
    required this.currentValue,
    required this.points,
    required this.isUnlocked,
    this.unlockedDate,
    required this.color,
  });

  /// نسبة التقدم (0.0 إلى 1.0)
  double get progress => targetValue > 0 ? currentValue / targetValue : 0.0;

  /// ما إذا كان الإنجاز مكتملاً
  bool get isCompleted => currentValue >= targetValue;

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'iconCodePoint': icon.codePoint,
      'type': type.index,
      'level': level.index,
      'targetValue': targetValue,
      'currentValue': currentValue,
      'points': points,
      'isUnlocked': isUnlocked ? 1 : 0,
      'unlockedDate': unlockedDate?.toIso8601String(),
      'colorValue': color.value,
    };
  }

  /// إنشاء من Map
  factory Achievement.fromMap(Map<String, dynamic> map) {
    return Achievement(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      icon: IconData(map['iconCodePoint'], fontFamily: 'MaterialIcons'),
      type: AchievementType.values[map['type']],
      level: AchievementLevel.values[map['level']],
      targetValue: map['targetValue'],
      currentValue: map['currentValue'],
      points: map['points'],
      isUnlocked: map['isUnlocked'] == 1,
      unlockedDate: map['unlockedDate'] != null
          ? DateTime.parse(map['unlockedDate'])
          : null,
      color: Color(map['colorValue']),
    );
  }

  /// نسخ مع تعديل بعض القيم
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    IconData? icon,
    AchievementType? type,
    AchievementLevel? level,
    int? targetValue,
    int? currentValue,
    int? points,
    bool? isUnlocked,
    DateTime? unlockedDate,
    Color? color,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      type: type ?? this.type,
      level: level ?? this.level,
      targetValue: targetValue ?? this.targetValue,
      currentValue: currentValue ?? this.currentValue,
      points: points ?? this.points,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedDate: unlockedDate ?? this.unlockedDate,
      color: color ?? this.color,
    );
  }
}

/// فئة مساعدة لإنشاء الإنجازات المحددة مسبقاً
class PredefinedAchievements {
  /// الحصول على قائمة الإنجازات الافتراضية
  static List<Achievement> getDefaultAchievements() {
    return [
      // إنجازات السلسلة المتتالية
      Achievement(
        id: 'streak_3',
        title: 'البداية المباركة',
        description: 'أكمل الأذكار لمدة 3 أيام متتالية',
        icon: Icons.local_fire_department,
        type: AchievementType.streak,
        level: AchievementLevel.bronze,
        targetValue: 3,
        currentValue: 0,
        points: 50,
        isUnlocked: false,
        color: const Color(0xFFCD7F32), // برونزي
      ),
      Achievement(
        id: 'streak_7',
        title: 'أسبوع من الذكر',
        description: 'أكمل الأذكار لمدة 7 أيام متتالية',
        icon: Icons.local_fire_department,
        type: AchievementType.streak,
        level: AchievementLevel.silver,
        targetValue: 7,
        currentValue: 0,
        points: 100,
        isUnlocked: false,
        color: const Color(0xFFC0C0C0), // فضي
      ),
      Achievement(
        id: 'streak_30',
        title: 'شهر من التقوى',
        description: 'أكمل الأذكار لمدة 30 يوماً متتالية',
        icon: Icons.local_fire_department,
        type: AchievementType.streak,
        level: AchievementLevel.gold,
        targetValue: 30,
        currentValue: 0,
        points: 300,
        isUnlocked: false,
        color: const Color(0xFFFFD700), // ذهبي
      ),

      // إنجازات يومية
      Achievement(
        id: 'daily_complete',
        title: 'يوم مبارك',
        description: 'أكمل جميع الأذكار في يوم واحد',
        icon: Icons.check_circle,
        type: AchievementType.daily,
        level: AchievementLevel.bronze,
        targetValue: 1,
        currentValue: 0,
        points: 25,
        isUnlocked: false,
        color: const Color(0xFFCD7F32),
      ),

      // إنجازات إجمالية
      Achievement(
        id: 'total_100',
        title: 'مئة ذكر',
        description: 'أكمل 100 ذكر في المجموع',
        icon: Icons.star,
        type: AchievementType.total,
        level: AchievementLevel.bronze,
        targetValue: 100,
        currentValue: 0,
        points: 75,
        isUnlocked: false,
        color: const Color(0xFFCD7F32),
      ),
      Achievement(
        id: 'total_500',
        title: 'خمسمئة ذكر',
        description: 'أكمل 500 ذكر في المجموع',
        icon: Icons.star,
        type: AchievementType.total,
        level: AchievementLevel.silver,
        targetValue: 500,
        currentValue: 0,
        points: 150,
        isUnlocked: false,
        color: const Color(0xFFC0C0C0),
      ),
      Achievement(
        id: 'total_1000',
        title: 'ألف ذكر',
        description: 'أكمل 1000 ذكر في المجموع',
        icon: Icons.star,
        type: AchievementType.total,
        level: AchievementLevel.gold,
        targetValue: 1000,
        currentValue: 0,
        points: 300,
        isUnlocked: false,
        color: const Color(0xFFFFD700),
      ),

      // إنجازات خاصة
      Achievement(
        id: 'morning_master',
        title: 'سيد أذكار الصباح',
        description: 'أكمل أذكار الصباح 30 مرة',
        icon: Icons.wb_sunny,
        type: AchievementType.special,
        level: AchievementLevel.gold,
        targetValue: 30,
        currentValue: 0,
        points: 200,
        isUnlocked: false,
        color: const Color(0xFFFFD700),
      ),
      Achievement(
        id: 'evening_master',
        title: 'سيد أذكار المساء',
        description: 'أكمل أذكار المساء 30 مرة',
        icon: Icons.nights_stay,
        type: AchievementType.special,
        level: AchievementLevel.gold,
        targetValue: 30,
        currentValue: 0,
        points: 200,
        isUnlocked: false,
        color: const Color(0xFFFFD700),
      ),
    ];
  }

  /// الحصول على لون المستوى
  static Color getLevelColor(AchievementLevel level) {
    switch (level) {
      case AchievementLevel.bronze:
        return const Color(0xFFCD7F32);
      case AchievementLevel.silver:
        return const Color(0xFFC0C0C0);
      case AchievementLevel.gold:
        return const Color(0xFFFFD700);
      case AchievementLevel.platinum:
        return const Color(0xFFE5E4E2);
      case AchievementLevel.diamond:
        return const Color(0xFFB9F2FF);
    }
  }

  /// الحصول على اسم المستوى
  static String getLevelName(AchievementLevel level) {
    switch (level) {
      case AchievementLevel.bronze:
        return 'برونزي';
      case AchievementLevel.silver:
        return 'فضي';
      case AchievementLevel.gold:
        return 'ذهبي';
      case AchievementLevel.platinum:
        return 'بلاتيني';
      case AchievementLevel.diamond:
        return 'ماسي';
    }
  }
}
