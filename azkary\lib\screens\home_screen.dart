import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';

import '../services/azkar_provider.dart';
import '../services/feedback_provider.dart';
import '../services/daily_ayah_provider.dart';
import '../models/azkar_model.dart';
import '../utils/logger.dart';
import 'azkar_list_screen.dart';
import 'custom_azkar_screen.dart';
import 'settings_screen.dart';
import '../widgets/page_transitions.dart';
import '../widgets/daily_allah_name.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/daily_ayah_dialog.dart';
import '../widgets/islamic_pattern.dart';
import '../widgets/islamic_background.dart';

import '../widgets/enhanced_animations.dart';
import '../widgets/prayer_times_bar.dart';
import '../services/prayer_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // التأكد من تحميل البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final provider = Provider.of<AzkarProvider>(context, listen: false);
      AppLogger.info(
        'Home screen initialized, categories count: ${provider.categories.length}',
      );

      // إذا كانت التصنيفات فارغة، قم بإعادة تحميل البيانات
      if (provider.categories.isEmpty) {
        AppLogger.info('Categories are empty, reloading data...');
        await _reloadData();
      } else {
        AppLogger.info('Categories already loaded:');
        for (var category in provider.categories) {
          AppLogger.info('- ${category.name} (${category.icon})');
        }
      }

      // تهيئة مزود أوقات الصلاة
      if (mounted) {
        final prayerProvider = Provider.of<PrayerProvider>(
          context,
          listen: false,
        );
        prayerProvider.initialize();
      }
    });
  }

  // دالة لإعادة تحميل البيانات
  Future<void> _reloadData() async {
    try {
      AppLogger.info('Reloading data from home screen');
      final provider = Provider.of<AzkarProvider>(context, listen: false);

      // إعادة تهيئة قاعدة البيانات وتحميل البيانات
      await provider.reloadData();

      AppLogger.info(
        'Data reloaded, categories count: ${provider.categories.length}',
      );
      if (provider.categories.isNotEmpty) {
        AppLogger.info('Categories loaded successfully:');
        for (var category in provider.categories) {
          AppLogger.info('- ${category.name} (${category.icon})');
        }
      } else {
        AppLogger.warning(
          'No categories loaded after reload, forcing another reload',
        );
        // محاولة إعادة التحميل مرة أخرى
        await provider.reloadData();
      }

      // تحديث واجهة المستخدم
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      AppLogger.error('Error reloading data from home screen: $e');
      // محاولة إعادة التحميل في حالة الخطأ
      if (mounted) {
        final provider = Provider.of<AzkarProvider>(context, listen: false);
        // محاولة إعادة التحميل مرة أخرى
        await provider.reloadData();
      }

      if (mounted) {
        setState(() {});
      }
    }
  }

  // دالة لتنسيق التاريخ بالعربية
  String _getFormattedDate() {
    final now = DateTime.now();
    final formatter = DateFormat('EEEE، d MMMM yyyy', 'ar');
    return formatter.format(now);
  }

  @override
  Widget build(BuildContext context) {
    // استخدام ألوان السمة بدلاً من الألوان الثابتة
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'أذكاري',
        actions: [
          // زر الإعدادات
          IconButton(
            icon: Icon(
              Icons.settings,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
            tooltip: 'الإعدادات',
            onPressed: () {
              HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
              Navigator.push(
                context,
                PageTransition(
                  type: PageTransitionType.rightToLeftWithFade,
                  child: const SettingsScreen(),
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut,
                ),
              );
            },
          ),
        ],
        leading: IconButton(
          icon: Icon(Icons.menu, color: theme.colorScheme.onSurface, size: 24),
          onPressed: () {
            HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
            Scaffold.of(context).openDrawer();
          },
        ),
      ),
      // تم إزالة زر الإضافة العائم واستبداله بزر في شبكة التصنيفات
      body: IslamicBackground(
        primaryColor: theme.colorScheme.primary,
        secondaryColor: theme.colorScheme.secondary,
        opacity: 0.04,
        showGradient: true,
        showPattern: true,
        child: Consumer<AzkarProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                  ),
                );
              }

              // التحقق من وجود البيانات
              if (provider.categories.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'لا توجد بيانات متاحة',
                        style: TextStyle(fontSize: 18),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _reloadData,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('إعادة تحميل البيانات'),
                      ),
                    ],
                  ),
                );
              }

              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // شريط أوقات الصلاة
                    const PrayerTimesBar(),

                    // قسم ذكر اليوم
                    if (provider.dailyZikr != null)
                      _buildDailyZikrSection(provider.dailyZikr!),

                    // قسم تصنيفات الأذكار
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'تصنيفات الأذكار',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color:
                              theme
                                  .colorScheme
                                  .onSurface, // استخدام لون النص على السطح من السمة
                        ),
                      ),
                    ),

                    // شبكة التصنيفات
                    _buildCategoriesGrid(provider.categories),

                    // قسم الأذكار الخاصة
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'أذكاري الخاصة',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          Text(
                            '${provider.customAzkar.length} ذكر',
                            style: TextStyle(
                              fontSize: 14,
                              color: theme.colorScheme.onSurface.withAlpha(153),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // زر الأذكار الخاصة
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const CustomAzkarScreen(),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          // الحصول على حجم الشاشة
                          padding: EdgeInsets.all(
                            MediaQuery.of(context).size.width < 360 ? 14 : 18,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: theme.brightness == Brightness.dark
                                  ? [
                                      theme.cardColor,
                                      theme.cardColor.withAlpha(240),
                                    ]
                                  : [
                                      const Color(0xFFFDFDFD), // أبيض فاتح جداً
                                      const Color(0xFFFBFBFB), // أبيض مع لمسة رمادية ناعمة
                                      Colors.white,
                                    ],
                            ),
                            borderRadius: BorderRadius.circular(16), // زوايا أكثر استدارة
                            border: Border.all(
                              color: theme.brightness == Brightness.dark
                                  ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                                  : Colors.grey.withAlpha(20), // حدود أخف
                              width: 0.8, // حدود أرفع
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: theme.brightness == Brightness.dark
                                    ? Colors.black.withAlpha(40) // ظل مثل تويتر
                                    : Colors.black.withAlpha(12), // ظل أخف جداً
                                spreadRadius: 0,
                                blurRadius: theme.brightness == Brightness.dark ? 5 : 12,
                                offset: const Offset(0, 4),
                              ),
                              if (theme.brightness == Brightness.light)
                                BoxShadow(
                                  color: Colors.white.withAlpha(200), // ظل أبيض للإضاءة الناعمة
                                  spreadRadius: 0,
                                  blurRadius: 6,
                                  offset: const Offset(0, -2),
                                ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  color: Colors.purple.withAlpha(51),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.bookmark,
                                  color: Colors.purple,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'أذكاري الخاصة',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'أضف وعدل أذكارك الخاصة',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: theme.colorScheme.onSurface
                                            .withAlpha(153),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                                color: theme.colorScheme.onSurface.withAlpha(
                                  153,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // قسم اسم الله اليومي
                    const Padding(
                      padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
                      child: Text(
                        'اسم الله اليومي',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    // مكون اسم الله اليومي
                    const DailyAllahName(),

                    // زر عرض الآية اليومية
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // تهيئة مزود الآية اليومية
                            final provider = Provider.of<DailyAyahProvider>(
                              context,
                              listen: false,
                            );
                            provider.initialize();

                            // عرض نافذة الآية اليومية
                            showDialog(
                              context: context,
                              builder: (context) => const DailyAyahDialog(),
                            );
                          },
                          icon: const Icon(Icons.menu_book),
                          label: const Text(
                            'عرض آية اليوم',
                            style: TextStyle(fontSize: 16),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              vertical: 16,
                              horizontal: 16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    )
  }

  // بناء قسم ذكر اليوم المحسن والمتجاوب
  Widget _buildDailyZikrSection(Zikr dailyZikr) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    // تحديد الأحجام والمسافات حسب حجم الشاشة
    final double margin = isSmallScreen ? 12 : 16;
    final double padding = isSmallScreen ? 14 : 16;
    final double borderRadius = isSmallScreen ? 16 : 20;
    final double titleSize = isSmallScreen ? 16 : 18;
    final double textSize = isSmallScreen ? 15 : 16;
    final double sourceSize = isSmallScreen ? 11 : 12;
    final double dateSize = isSmallScreen ? 10 : 12;

    return Hero(
      tag: 'daily-zikr',
      child: Container(
        margin: EdgeInsets.all(margin),
        padding: EdgeInsets.all(padding),
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: theme.brightness == Brightness.dark
                ? [
                    theme.colorScheme.primary.withAlpha(15),
                    theme.colorScheme.secondary.withAlpha(10),
                    theme.colorScheme.surface,
                  ]
                : [
                    const Color(0xFFFDFDFD), // أبيض فاتح جداً
                    theme.colorScheme.primary.withAlpha(4), // لمسة لونية خفيفة جداً
                    theme.colorScheme.secondary.withAlpha(3),
                    const Color(0xFFFBFBFB), // أبيض مع لمسة رمادية ناعمة
                    Colors.white,
                  ],
          ),
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color: theme.brightness == Brightness.dark
                ? theme.colorScheme.primary.withAlpha(30)
                : theme.colorScheme.primary.withAlpha(15), // حدود أخف
            width: 1.0, // حدود أرفع
          ),
          boxShadow: [
            BoxShadow(
              color: theme.brightness == Brightness.dark
                  ? theme.colorScheme.primary.withAlpha(20)
                  : theme.colorScheme.primary.withAlpha(10), // ظل أخف
              spreadRadius: 0,
              blurRadius: theme.brightness == Brightness.dark ? 12 : 16,
              offset: const Offset(0, 6),
            ),
            if (theme.brightness == Brightness.light)
              BoxShadow(
                color: Colors.white.withAlpha(200), // ظل أبيض للإضاءة الناعمة
                spreadRadius: 0,
                blurRadius: 8,
                offset: const Offset(0, -3),
              ),
            BoxShadow(
              color: theme.brightness == Brightness.dark
                  ? Colors.black.withAlpha(30)
                  : Colors.black.withAlpha(8), // ظل أخف جداً
              spreadRadius: 0,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // العنوان والتاريخ المحسن
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 10 : 12,
                      vertical: isSmallScreen ? 6 : 8,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          theme.colorScheme.primary.withAlpha(30),
                          theme.colorScheme.primary.withAlpha(15),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(
                        isSmallScreen ? 16 : 20,
                      ),
                      border: Border.all(
                        color: theme.colorScheme.primary.withAlpha(40),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withAlpha(80),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.auto_awesome,
                            color: Colors.white,
                            size: isSmallScreen ? 16 : 18,
                          ),
                        ),
                        SizedBox(width: isSmallScreen ? 6 : 8),
                        Flexible(
                          child: Text(
                            'ذكري اليومي',
                            style: TextStyle(
                              fontSize: titleSize,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 6 : 8,
                    vertical: isSmallScreen ? 3 : 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondary.withAlpha(20),
                    borderRadius: BorderRadius.circular(
                      isSmallScreen ? 10 : 12,
                    ),
                    border: Border.all(
                      color: theme.colorScheme.secondary.withAlpha(40),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: theme.colorScheme.secondary,
                        size: isSmallScreen ? 10 : 12,
                      ),
                      SizedBox(width: isSmallScreen ? 3 : 4),
                      Text(
                        _getFormattedDate(),
                        style: TextStyle(
                          fontSize: dateSize,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.secondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 14 : 16),

            // نص الذكر مع تحسينات
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface.withAlpha(50),
                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(20),
                  width: 1,
                ),
              ),
              child: Text(
                dailyZikr.text,
                style: TextStyle(
                  fontSize: textSize,
                  height: 1.6,
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            ),
            SizedBox(height: isSmallScreen ? 10 : 12),

            // المصدر والأزرار
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 8 : 10,
                      vertical: isSmallScreen ? 4 : 6,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.tertiary.withAlpha(15),
                      borderRadius: BorderRadius.circular(
                        isSmallScreen ? 8 : 10,
                      ),
                      border: Border.all(
                        color: theme.colorScheme.tertiary.withAlpha(30),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.menu_book,
                          color: theme.colorScheme.tertiary,
                          size: isSmallScreen ? 12 : 14,
                        ),
                        SizedBox(width: isSmallScreen ? 4 : 6),
                        Flexible(
                          child: Text(
                            'رواه ${dailyZikr.source}',
                            style: TextStyle(
                              fontSize: sourceSize,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.tertiary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildModernActionButton(
                      Icons.copy_outlined,
                      'نسخ',
                      theme.colorScheme.primary,
                      isSmallScreen,
                      onPressed: () => _copyToClipboard(dailyZikr.text),
                    ),
                    SizedBox(width: isSmallScreen ? 8 : 12),
                    _buildModernActionButton(
                      Icons.share_outlined,
                      'مشاركة',
                      theme.colorScheme.secondary,
                      isSmallScreen,
                      onPressed: () => _shareZikr(dailyZikr),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء زر عمل حديث ومحسن
  Widget _buildModernActionButton(
    IconData icon,
    String tooltip,
    Color color,
    bool isSmallScreen, {
    VoidCallback? onPressed,
  }) {
    final size = isSmallScreen ? 32.0 : 36.0;
    final iconSize = isSmallScreen ? 16.0 : 18.0;

    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(size / 2),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [color.withAlpha(30), color.withAlpha(15)],
          ),
          shape: BoxShape.circle,
          border: Border.all(color: color.withAlpha(60), width: 1),
          boxShadow: [
            BoxShadow(
              color: color.withAlpha(20),
              blurRadius: 4,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Tooltip(
          message: tooltip,
          child: Icon(icon, size: iconSize, color: color),
        ),
      ),
    );
  }

  // نسخ النص إلى الحافظة
  void _copyToClipboard(String text) {
    // تنفيذ نسخ النص إلى الحافظة
    Clipboard.setData(ClipboardData(text: text));

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showSuccessSnackBar(
      context,
      'تم نسخ النص',
      icon: Icons.check_circle,
    );
  }

  // مشاركة الذكر
  void _shareZikr(Zikr zikr) {
    // تنفيذ مشاركة الذكر
    final text = '${zikr.text}\n\nرواه ${zikr.source}\n\nمن تطبيق أذكاري';

    // استخدام SharePlus بالطريقة الصحيحة
    final box = context.findRenderObject() as RenderBox?;
    SharePlus.instance.share(
      ShareParams(
        text: text,
        sharePositionOrigin:
            box != null ? box.localToGlobal(Offset.zero) & box.size : null,
      ),
    );

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showInfoSnackBar(context, 'تمت مشاركة الذكر');
  }

  // بناء شبكة التصنيفات
  Widget _buildCategoriesGrid(List<Category> categories) {
    // الحصول على حجم الشاشة
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isMediumScreen = screenSize.width >= 360 && screenSize.width < 600;

    // تحديد عدد الأعمدة بناءً على حجم الشاشة
    // تعديل: جعل عدد الأعمدة 2 حتى في الشاشات الصغيرة
    int crossAxisCount = isSmallScreen ? 2 : (isMediumScreen ? 2 : 3);

    // تحديد نسبة العرض إلى الارتفاع
    double childAspectRatio =
        isSmallScreen ? 1.0 : (isMediumScreen ? 1.0 : 1.1);

    // إنشاء قائمة جديدة تحتوي على التصنيفات الحالية
    final List<Widget> gridItems = [];

    // إضافة عناصر التصنيفات الحالية
    for (var category in categories) {
      gridItems.add(_buildCategoryItem(category));
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: isSmallScreen ? 8 : 16,
        mainAxisSpacing: isSmallScreen ? 8 : 16,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: gridItems.length,
      itemBuilder: (context, index) {
        return gridItems[index];
      },
    );
  }

  // بناء عنصر التصنيف المحسن والمتجاوب
  Widget _buildCategoryItem(Category category) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isMediumScreen = screenSize.width < 600;

    // تحديد الأحجام حسب حجم الشاشة
    final double iconSize = isSmallScreen ? 44 : (isMediumScreen ? 52 : 56);
    final double iconInnerSize =
        isSmallScreen ? 22 : (isMediumScreen ? 26 : 28);
    final double titleSize = isSmallScreen ? 13 : (isMediumScreen ? 15 : 16);
    final double countSize = isSmallScreen ? 9 : (isMediumScreen ? 11 : 12);
    final double padding = isSmallScreen ? 10 : (isMediumScreen ? 14 : 16);
    final double borderRadius = isSmallScreen ? 16 : (isMediumScreen ? 18 : 20);

    // تحديد الأيقونة والألوان
    IconData iconData;
    Color iconBgColor;
    Color iconColor;

    if (category.name.contains('الصباح')) {
      iconData = Icons.wb_sunny_outlined;
      iconBgColor = Colors.amber.withAlpha(40);
      iconColor = Colors.amber.shade700;
    } else if (category.name.contains('المساء')) {
      iconData = Icons.nightlight_round;
      iconBgColor = Colors.indigo.withAlpha(40);
      iconColor = Colors.indigo.shade700;
    } else if (category.name.contains('النوم')) {
      iconData = Icons.bed;
      iconBgColor = Colors.teal.withAlpha(40);
      iconColor = Colors.teal.shade700;
    } else if (category.name.contains('الاستيقاظ')) {
      iconData = Icons.alarm;
      iconBgColor = Colors.orange.withAlpha(40);
      iconColor = Colors.orange.shade700;
    } else if (category.name.contains('الصلاة')) {
      iconData = Icons.mosque;
      iconBgColor = Colors.green.withAlpha(40);
      iconColor = Colors.green.shade700;
    } else if (category.name.contains('الاستغفار')) {
      iconData = Icons.favorite;
      iconBgColor = Colors.red.withAlpha(40);
      iconColor = Colors.red.shade700;
    } else {
      // الحالة الافتراضية
      switch (category.icon) {
        case 'sun':
          iconData = Icons.wb_sunny_outlined;
          iconBgColor = Colors.amber.withAlpha(40);
          iconColor = Colors.amber.shade700;
          break;
        case 'moon':
          iconData = Icons.nightlight_round;
          iconBgColor = Colors.indigo.withAlpha(40);
          iconColor = Colors.indigo.shade700;
          break;
        case 'bed':
          iconData = Icons.bed;
          iconBgColor = Colors.teal.withAlpha(40);
          iconColor = Colors.teal.shade700;
          break;
        case 'alarm':
          iconData = Icons.alarm;
          iconBgColor = Colors.orange.withAlpha(40);
          iconColor = Colors.orange.shade700;
          break;
        case 'prayer':
          iconData = Icons.mosque;
          iconBgColor = Colors.green.withAlpha(40);
          iconColor = Colors.green.shade700;
          break;
        case 'heart':
          iconData = Icons.favorite;
          iconBgColor = Colors.red.withAlpha(40);
          iconColor = Colors.red.shade700;
          break;
        case 'travel':
          iconData = Icons.directions_car;
          iconBgColor = Colors.brown.withAlpha(40);
          iconColor = Colors.brown.shade700;
          break;
        case 'food':
          iconData = Icons.restaurant;
          iconBgColor = Colors.yellow.withAlpha(40);
          iconColor = Colors.yellow.shade700;
          break;
        default:
          iconData = Icons.auto_awesome;
          iconBgColor = Colors.blue.withAlpha(40);
          iconColor = Colors.blue.shade700;
      }
    }

    return FadeInAnimation(
      delay: Duration(milliseconds: category.id * 100),
      child: Hero(
        tag: 'category-${category.id}',
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              PageTransition(
                type: PageTransitionType.rightToLeftWithFade,
                child: AzkarListScreen(category: category.name),
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              ),
            );
          },
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: EdgeInsets.all(padding),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: theme.brightness == Brightness.dark
                    ? [
                        iconColor.withAlpha(8),
                        iconColor.withAlpha(4),
                        theme.colorScheme.surface,
                      ]
                    : [
                        const Color(0xFFFDFDFD), // أبيض فاتح جداً
                        iconColor.withAlpha(3), // لمسة لونية خفيفة جداً
                        const Color(0xFFFBFBFB), // أبيض مع لمسة رمادية ناعمة
                        Colors.white,
                      ],
              ),
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: theme.brightness == Brightness.dark
                    ? iconColor.withAlpha(30)
                    : iconColor.withAlpha(15), // حدود أخف في الوضع الفاتح
                width: 0.8, // حدود أرفع
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.brightness == Brightness.dark
                      ? iconColor.withAlpha(15)
                      : iconColor.withAlpha(8), // ظل أخف
                  blurRadius: theme.brightness == Brightness.dark ? 8 : 12,
                  spreadRadius: 0,
                  offset: const Offset(0, 4),
                ),
                if (theme.brightness == Brightness.light)
                  BoxShadow(
                    color: Colors.white.withAlpha(200), // ظل أبيض للإضاءة الناعمة
                    blurRadius: 6,
                    spreadRadius: 0,
                    offset: const Offset(0, -2),
                  ),
                BoxShadow(
                  color: theme.brightness == Brightness.dark
                      ? Colors.black.withAlpha(20)
                      : Colors.black.withAlpha(6), // ظل أخف جداً
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة محسنة مع تدرج ناعم
                Container(
                  width: iconSize,
                  height: iconSize,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: theme.brightness == Brightness.dark
                          ? [iconBgColor, iconColor.withAlpha(30)]
                          : [
                              iconBgColor.withAlpha(40), // أفتح في الوضع الفاتح
                              iconColor.withAlpha(15), // شفافية أكثر
                              Colors.white.withAlpha(180),
                            ],
                    ),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: theme.brightness == Brightness.dark
                          ? iconColor.withAlpha(60)
                          : iconColor.withAlpha(30), // حدود أخف
                      width: 1.5, // حدود أرفع
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: theme.brightness == Brightness.dark
                            ? iconColor.withAlpha(30)
                            : iconColor.withAlpha(15), // ظل أخف
                        blurRadius: theme.brightness == Brightness.dark ? 6 : 8,
                        spreadRadius: 0,
                        offset: const Offset(0, 3),
                      ),
                      if (theme.brightness == Brightness.light)
                        BoxShadow(
                          color: Colors.white.withAlpha(220),
                          blurRadius: 4,
                          spreadRadius: 0,
                          offset: const Offset(0, -1),
                        ),
                    ],
                  ),
                  child: Icon(
                    iconData,
                    color: iconColor.withAlpha(220), // لون أيقونة أخف قليلاً
                    size: iconInnerSize,
                  ),
                ),
                SizedBox(
                  height: isSmallScreen ? 10 : (isMediumScreen ? 12 : 14),
                ),

                // اسم التصنيف
                Text(
                  category.name,
                  style: TextStyle(
                    fontSize: titleSize,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: isSmallScreen ? 6 : (isMediumScreen ? 8 : 10)),

                // عدد الأذكار مع تصميم محسن وناعم
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 8 : (isMediumScreen ? 10 : 12),
                    vertical: isSmallScreen ? 4 : (isMediumScreen ? 5 : 6),
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: theme.brightness == Brightness.dark
                          ? [
                              iconColor.withAlpha(40),
                              iconColor.withAlpha(20),
                            ]
                          : [
                              iconColor.withAlpha(20), // أفتح في الوضع الفاتح
                              iconColor.withAlpha(10),
                              Colors.white.withAlpha(200),
                            ],
                    ),
                    borderRadius: BorderRadius.circular(
                      isSmallScreen ? 12 : 14, // زوايا أكثر استدارة
                    ),
                    border: Border.all(
                      color: theme.brightness == Brightness.dark
                          ? iconColor.withAlpha(60)
                          : iconColor.withAlpha(30), // حدود أخف
                      width: 0.8, // حدود أرفع
                    ),
                    boxShadow: [
                      if (theme.brightness == Brightness.light)
                        BoxShadow(
                          color: iconColor.withAlpha(10),
                          blurRadius: 4,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.format_list_bulleted,
                        color: theme.brightness == Brightness.dark
                            ? iconColor
                            : iconColor.withAlpha(200), // أخف في الوضع الفاتح
                        size: isSmallScreen ? 10 : 12,
                      ),
                      SizedBox(width: isSmallScreen ? 3 : 4),
                      Text(
                        '${category.count} ذكر',
                        style: TextStyle(
                          fontSize: countSize,
                          fontWeight: FontWeight.bold,
                          color: theme.brightness == Brightness.dark
                              ? iconColor
                              : iconColor.withAlpha(200), // أخف في الوضع الفاتح
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
