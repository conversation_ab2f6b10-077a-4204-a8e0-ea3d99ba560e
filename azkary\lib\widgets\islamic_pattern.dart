import 'package:flutter/material.dart';
import 'dart:math' as math;

/// مكون لرسم زخارف إسلامية
class IslamicPattern extends StatelessWidget {
  final Color? color;
  final double opacity;

  const IslamicPattern({super.key, this.color, this.opacity = 0.1});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // تحديد اللون والشفافية بناءً على الوضع
    Color patternColor = color ?? theme.colorScheme.primary;
    double finalOpacity = opacity;

    // تعديل الشفافية حسب الوضع
    if (isDarkMode) {
      // في الأوضاع الداكنة، نقلل الشفافية لتجنب التداخل
      finalOpacity = opacity * 0.7;
      // في الوضع المعتم (تويتر)، نستخدم لون أفتح قليلاً
      if (theme.scaffoldBackgroundColor == const Color(0xFF15202B)) {
        patternColor = patternColor.withAlpha(100);
        finalOpacity = opacity * 0.6; // شفافية أقل للوضع المعتم
      }
    }

    return CustomPaint(
      painter: IslamicPatternPainter(
        color: patternColor,
        opacity: finalOpacity,
      ),
      size: Size.infinite,
    );
  }
}

/// رسام الزخارف الإسلامية
class IslamicPatternPainter extends CustomPainter {
  final Color color;
  final double opacity;

  IslamicPatternPainter({required this.color, required this.opacity});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color.withAlpha((opacity * 255).round())
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    final patternSize = 80.0;
    final horizontalCount = (size.width / patternSize).ceil() + 1;
    final verticalCount = (size.height / patternSize).ceil() + 1;

    for (int i = -1; i < horizontalCount; i++) {
      for (int j = -1; j < verticalCount; j++) {
        final centerX = i * patternSize;
        final centerY = j * patternSize;

        // رسم النمط الإسلامي
        _drawIslamicPattern(
          canvas,
          paint,
          Offset(centerX, centerY),
          patternSize,
        );
      }
    }
  }

  void _drawIslamicPattern(
    Canvas canvas,
    Paint paint,
    Offset center,
    double size,
  ) {
    // رسم نجمة ثمانية
    _drawEightPointedStar(canvas, paint, center, size * 0.4);

    // رسم دائرة خارجية
    canvas.drawCircle(center, size * 0.45, paint);

    // رسم مثمن
    _drawOctagon(canvas, paint, center, size * 0.35);
  }

  void _drawEightPointedStar(
    Canvas canvas,
    Paint paint,
    Offset center,
    double radius,
  ) {
    final path = Path();

    for (int i = 0; i < 8; i++) {
      final angle = i * math.pi / 4;
      final outerX = center.dx + radius * math.cos(angle);
      final outerY = center.dy + radius * math.sin(angle);

      final innerAngle = angle + math.pi / 8;
      final innerRadius = radius * 0.4;
      final innerX = center.dx + innerRadius * math.cos(innerAngle);
      final innerY = center.dy + innerRadius * math.sin(innerAngle);

      if (i == 0) {
        path.moveTo(outerX, outerY);
      } else {
        path.lineTo(outerX, outerY);
      }

      path.lineTo(innerX, innerY);
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  void _drawOctagon(Canvas canvas, Paint paint, Offset center, double radius) {
    final path = Path();

    for (int i = 0; i < 8; i++) {
      final angle = i * math.pi / 4 + math.pi / 8;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(IslamicPatternPainter oldDelegate) {
    return oldDelegate.color != color || oldDelegate.opacity != opacity;
  }
}
