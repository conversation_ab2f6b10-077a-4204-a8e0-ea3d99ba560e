import 'package:flutter/material.dart';
import '../services/azkar_settings_service.dart';

// استخدام StatefulWidget لتتبع حالة النقر مع تأثيرات محسنة
class MiniZikrCounter extends StatefulWidget {
  final int currentCount;
  final int totalCount;
  final Function() onIncrement;
  final Function()? onReset;

  const MiniZikrCounter({
    super.key,
    required this.currentCount,
    required this.totalCount,
    required this.onIncrement,
    this.onReset,
  });

  @override
  State<MiniZikrCounter> createState() => _MiniZikrCounterState();
}

class _MiniZikrCounterState extends State<MiniZikrCounter>
    with TickerProviderStateMixin {
  // متغير لتتبع حالة النقر
  bool _isPressed = false;

  // متحكمات الانيميشن
  late AnimationController _scaleController;
  late AnimationController _rippleController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rippleAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد متحكمات الانيميشن
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // إعداد الانيميشن
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );

    _rippleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rippleController, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rippleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isCompleted = widget.currentCount >= widget.totalCount;

    // إذا اكتمل العد، نعرض زر التسبيح المكتمل مع العداد بداخله
    if (isCompleted) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // زر التسبيح المكتمل (عرض كامل)
          SizedBox(
            width: double.infinity, // جعل الزر بعرض كامل
            child: ElevatedButton(
              onPressed: null, // تعطيل الزر عند اكتمال العد
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    theme.colorScheme.primary, // استخدام لون السمة الأساسي
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 10,
                ),
                textStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                disabledBackgroundColor:
                    theme
                        .colorScheme
                        .primary, // استخدام لون السمة الأساسي عند تعطيل الزر
                disabledForegroundColor:
                    Colors.white, // لون النص عند تعطيل الزر
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center, // توسيط المحتوى
                children: [
                  // أيقونة التسبيح المكتمل
                  const Icon(Icons.check_circle, size: 16),
                  const SizedBox(width: 8),

                  // نص التسبيح المكتمل
                  const Text('تم التسبيح'),

                  // فاصل
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    height: 20,
                    width: 1,
                    color: Colors.white.withValues(
                      red: 255,
                      green: 255,
                      blue: 255,
                      alpha: 0.4,
                    ), // 100/255 ≈ 0.4
                  ),

                  // العداد المكتمل
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '${widget.currentCount}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '/${widget.totalCount}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(
                            red: 255,
                            green: 255,
                            blue: 255,
                            alpha: 0.78,
                          ), // 200/255 ≈ 0.78
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // رسالة "أحسنت" مع تأثير انتقالي
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: theme.colorScheme.primary, // استخدام لون السمة الأساسي
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  'أحسنت',
                  style: TextStyle(
                    color:
                        theme.colorScheme.primary, // استخدام لون السمة الأساسي
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // تم إزالة زر إعادة الضبط
        ],
      );
    }

    // إذا لم يكتمل العد، نعرض زر التسبيح العادي مع العداد بداخله (عرض كامل)
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleController, _rippleController]),
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 - (_scaleAnimation.value * 0.05),
          child: Stack(
            children: [
              // تأثير الموجة
              if (_rippleAnimation.value > 0)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.colorScheme.primary.withValues(
                          alpha: 1.0 - _rippleAnimation.value,
                        ),
                        width: 2,
                      ),
                    ),
                  ),
                ),

              // الزر الرئيسي
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () async {
                    // تأثير اهتزاز محسن من خدمة الإعدادات
                    await AzkarSettingsService.performVibration();

                    // تشغيل تأثيرات الانيميشن
                    _scaleController.forward().then((_) {
                      _scaleController.reverse();
                    });

                    _rippleController.forward().then((_) {
                      _rippleController.reset();
                    });

                    // تنفيذ زيادة العداد
                    widget.onIncrement();

                    // تنفيذ تأثير النقر
                    setState(() {
                      _isPressed = true;

                      // إعادة الزر إلى حالته الطبيعية بعد فترة قصيرة
                      Future.delayed(const Duration(milliseconds: 150), () {
                        if (mounted) {
                          setState(() {
                            _isPressed = false;
                          });
                        }
                      });
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _isPressed
                            ? theme.colorScheme.primary.withValues(
                              red: theme.colorScheme.primary.r.toDouble(),
                              green: theme.colorScheme.primary.g.toDouble(),
                              blue: theme.colorScheme.primary.b.toDouble(),
                              alpha: 0.78,
                            ) // 200/255 ≈ 0.78
                            : theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    textStyle: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    elevation: _isPressed ? 1 : 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment:
                        MainAxisAlignment.center, // توسيط المحتوى
                    children: [
                      // أيقونة التسبيح
                      const Icon(Icons.add, size: 16),
                      const SizedBox(width: 8),

                      // نص التسبيح
                      const Text('التسبيح'),

                      // فاصل
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        height: 20,
                        width: 1,
                        color: Colors.white.withValues(
                          red: 255,
                          green: 255,
                          blue: 255,
                          alpha: 0.4,
                        ), // 100/255 ≈ 0.4
                      ),

                      // العداد
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // رقم العداد الحالي
                          Text(
                            '${widget.currentCount}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          // إجمالي العدد المطلوب
                          Text(
                            '/${widget.totalCount}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white.withValues(
                                red: 255,
                                green: 255,
                                blue: 255,
                                alpha: 0.78,
                              ), // 200/255 ≈ 0.78
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
