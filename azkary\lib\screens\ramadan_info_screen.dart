import 'package:flutter/material.dart';
import '../models/ramadan_model.dart';
import '../services/ramadan_service.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/enhanced_card.dart';
import '../widgets/enhanced_animations.dart';
import '../widgets/islamic_pattern.dart';

/// شاشة معلومات رمضان
class RamadanInfoScreen extends StatefulWidget {
  const RamadanInfoScreen({super.key});

  @override
  State<RamadanInfoScreen> createState() => _RamadanInfoScreenState();
}

class _RamadanInfoScreenState extends State<RamadanInfoScreen>
    with TickerProviderStateMixin {
  final RamadanService _ramadanService = RamadanService();
  late RamadanInfo _ramadanInfo;
  late AnimationController _moonController;
  late Animation<double> _moonAnimation;

  @override
  void initState() {
    super.initState();
    _ramadanInfo = _ramadanService.getCurrentRamadanInfo();

    // إعداد الرسوم المتحركة للقمر
    _moonController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    _moonAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _moonController, curve: Curves.easeInOut),
    );

    _moonController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _moonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: const CustomAppBar(title: 'معلومات رمضان'),
      body: Stack(
        children: [
          // خلفية رمضانية
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    RamadanColors.darkGreen.withValues(alpha: 0.1),
                    theme.scaffoldBackgroundColor,
                  ],
                ),
              ),
            ),
          ),

          // نمط إسلامي
          Positioned.fill(
            child: Opacity(
              opacity: 0.03,
              child: IslamicPattern(color: RamadanColors.gold),
            ),
          ),

          // المحتوى
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // بطاقة الحالة الحالية
                _buildCurrentStatusCard(),
                const SizedBox(height: 20),

                // بطاقة التواريخ المهمة
                _buildImportantDatesCard(),
                const SizedBox(height: 20),

                // بطاقة ساعات الصيام
                _buildFastingHoursCard(),
                const SizedBox(height: 20),

                // بطاقة العشر الأواخر
                _buildLastTenDaysCard(),
                const SizedBox(height: 20),

                // بطاقة ليلة القدر
                _buildLaylatAlQadrCard(),
                const SizedBox(height: 20),

                // بطاقة نصائح رمضانية
                _buildRamadanTipsCard(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الحالة الحالية
  Widget _buildCurrentStatusCard() {
    return FadeInAnimation(
      delay: const Duration(milliseconds: 200),
      child: EnhancedCard(
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient:
                _ramadanInfo.isCurrentlyRamadan
                    ? RamadanColors.ramadanGradient
                    : LinearGradient(
                      colors: [
                        RamadanColors.emerald.withValues(alpha: 0.3),
                        RamadanColors.darkGreen.withValues(alpha: 0.1),
                      ],
                    ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              // أيقونة متحركة
              AnimatedBuilder(
                animation: _moonAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(0, _moonAnimation.value * 10 - 5),
                    child: Icon(
                      RamadanIcons.crescent,
                      size: 48,
                      color:
                          _ramadanInfo.isCurrentlyRamadan
                              ? Colors.white
                              : RamadanColors.gold,
                    ),
                  );
                },
              ),
              const SizedBox(height: 16),

              // النص الرئيسي
              Text(
                _ramadanInfo.isCurrentlyRamadan
                    ? 'نحن في شهر رمضان المبارك'
                    : 'في انتظار شهر رمضان',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color:
                      _ramadanInfo.isCurrentlyRamadan
                          ? Colors.white
                          : RamadanColors.darkGreen,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              // النص الفرعي
              Text(
                _ramadanInfo.isCurrentlyRamadan
                    ? 'باقي ${_ramadanInfo.daysRemaining} يوم على انتهاء الشهر الكريم'
                    : 'باقي ${_ramadanInfo.timeUntilRamadan.inDays} يوم على بداية رمضان',
                style: TextStyle(
                  fontSize: 16,
                  color:
                      _ramadanInfo.isCurrentlyRamadan
                          ? Colors.white70
                          : RamadanColors.darkGreen.withValues(alpha: 0.8),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة التواريخ المهمة
  Widget _buildImportantDatesCard() {
    return FadeInAnimation(
      delay: const Duration(milliseconds: 400),
      child: EnhancedCard(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    RamadanIcons.calendar,
                    color: RamadanColors.gold,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'التواريخ المهمة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: RamadanColors.darkGreen,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              _buildDateItem(
                'بداية رمضان ${_ramadanInfo.year}',
                _formatDate(_ramadanInfo.startDate),
                RamadanIcons.crescent,
              ),
              const SizedBox(height: 12),

              _buildDateItem(
                'نهاية رمضان ${_ramadanInfo.year}',
                _formatDate(_ramadanInfo.endDate),
                RamadanIcons.star,
              ),
              const SizedBox(height: 12),

              _buildDateItem(
                'العشر الأواخر',
                _formatDate(
                  _ramadanInfo.endDate.subtract(const Duration(days: 10)),
                ),
                RamadanIcons.mosque,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر التاريخ
  Widget _buildDateItem(String title, String date, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: RamadanColors.gold.withValues(alpha: 0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: RamadanColors.gold, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
              ),
              Text(
                date,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة ساعات الصيام
  Widget _buildFastingHoursCard() {
    return FadeInAnimation(
      delay: const Duration(milliseconds: 600),
      child: EnhancedCard(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    RamadanIcons.timer,
                    color: RamadanColors.emerald,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'ساعات الصيام',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: RamadanColors.darkGreen,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: RamadanColors.emerald.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      '${_ramadanInfo.fastingHours.inHours} ساعة و ${_ramadanInfo.fastingHours.inMinutes % 60} دقيقة',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: RamadanColors.emerald,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'متوسط ساعات الصيام اليومية',
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة العشر الأواخر
  Widget _buildLastTenDaysCard() {
    return FadeInAnimation(
      delay: const Duration(milliseconds: 800),
      child: EnhancedCard(
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient:
                _ramadanInfo.isLastTenDays
                    ? LinearGradient(
                      colors: [
                        RamadanColors.gold.withValues(alpha: 0.3),
                        RamadanColors.bronze.withValues(alpha: 0.1),
                      ],
                    )
                    : null,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    RamadanIcons.mosque,
                    color:
                        _ramadanInfo.isLastTenDays
                            ? RamadanColors.gold
                            : RamadanColors.bronze,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'العشر الأواخر من رمضان',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color:
                            _ramadanInfo.isLastTenDays
                                ? RamadanColors.gold
                                : RamadanColors.darkGreen,
                      ),
                    ),
                  ),
                  if (_ramadanInfo.isLastTenDays)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: RamadanColors.gold,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'الآن',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 16),

              Text(
                _ramadanInfo.isLastTenDays
                    ? 'نحن الآن في العشر الأواخر المباركة من رمضان. هذا وقت الاعتكاف والدعاء وطلب ليلة القدر.'
                    : 'العشر الأواخر من رمضان هي أفضل ليالي السنة، وفيها ليلة القدر التي هي خير من ألف شهر.',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(height: 1.6),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة ليلة القدر
  Widget _buildLaylatAlQadrCard() {
    return FadeInAnimation(
      delay: const Duration(milliseconds: 1000),
      child: EnhancedCard(
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient:
                _ramadanInfo.isPotentialLaylatAlQadr
                    ? LinearGradient(
                      colors: [
                        RamadanColors.gold.withValues(alpha: 0.4),
                        RamadanColors.darkGreen.withValues(alpha: 0.2),
                      ],
                    )
                    : null,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(RamadanIcons.star, color: RamadanColors.gold, size: 28),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'ليلة القدر',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: RamadanColors.gold,
                      ),
                    ),
                  ),
                  if (_ramadanInfo.isPotentialLaylatAlQadr)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: RamadanColors.gold,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'ليلة وترية',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 16),

              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: RamadanColors.gold.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      '"ليلة القدر خير من ألف شهر"',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: RamadanColors.gold,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'سورة القدر',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: RamadanColors.bronze,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),

              Text(
                _ramadanInfo.isPotentialLaylatAlQadr
                    ? 'هذه ليلة وترية من العشر الأواخر، قد تكون ليلة القدر. أكثروا من الدعاء والذكر.'
                    : 'ليلة القدر في العشر الأواخر من رمضان، وهي أرجح ما تكون في الليالي الوترية.',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(height: 1.6),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة نصائح رمضانية
  Widget _buildRamadanTipsCard() {
    final tips = [
      'أكثر من قراءة القرآن والتدبر في معانيه',
      'احرص على صلاة التراويح والقيام',
      'أكثر من الدعاء خاصة عند الإفطار',
      'تصدق وأطعم الطعام للفقراء',
      'اعتكف في العشر الأواخر إن استطعت',
      'احرص على الأذكار والتسبيح',
    ];

    return FadeInAnimation(
      delay: const Duration(milliseconds: 1200),
      child: EnhancedCard(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    RamadanIcons.book,
                    color: RamadanColors.emerald,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'نصائح رمضانية',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: RamadanColors.darkGreen,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              ...tips.asMap().entries.map((entry) {
                final index = entry.key;
                final tip = entry.value;

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: RamadanColors.emerald,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          tip,
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(height: 1.5),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
