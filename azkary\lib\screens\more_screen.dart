import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/islamic_pattern.dart';
import 'prayer_times_screen.dart';
import 'allah_names_screen.dart';
import 'qibla_screen.dart';
import 'settings_screen.dart';
import 'duaa_screen.dart';
import 'tasbih_screen.dart';
import 'statistics_screen.dart';
import 'ramadan_screen.dart';
import '../widgets/page_transitions.dart';
import '../widgets/custom_app_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../models/ramadan_model.dart';
import '../services/ramadan_service.dart';

/// شاشة "المزيد" التي تحتوي على خيارات إضافية
class MoreScreen extends StatefulWidget {
  const MoreScreen({super.key});

  @override
  State<MoreScreen> createState() => _MoreScreenState();
}

class _MoreScreenState extends State<MoreScreen>
    with SingleTickerProviderStateMixin {
  // متحكم الحركة للتأثيرات
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    // إعداد متحكم الحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // بدء الحركة عند تحميل الصفحة
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'المزيد',
        leading: Container(), // إزالة زر الرجوع
      ),
      body: Stack(
        children: [
          // خلفية إسلامية
          Positioned.fill(
            child: Opacity(
              opacity: 0.05,
              child: IslamicPattern(color: theme.colorScheme.primary),
            ),
          ),

          // محتوى الصفحة
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // عنوان توضيحي
                    Text(
                      'الميزات الإسلامية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // شبكة الميزات الإسلامية
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8.0,
                        horizontal: 4.0,
                      ),
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          // تحديد المسافة بين العناصر بناءً على عرض الشاشة
                          final spacing =
                              constraints.maxWidth < 360 ? 6.0 : 8.0;

                          return Column(
                            mainAxisSize:
                                MainAxisSize
                                    .min, // تقليل حجم العمود للحد الأدنى المطلوب
                            children: [
                              // الصف الأول: أوقات الصلاة، المسبحة، أسماء الله الحسنى
                              Row(
                                children: [
                                  // أوقات الصلاة
                                  Expanded(
                                    child: _buildFeatureGridItem(
                                      context: context,
                                      icon: Icons.access_time,
                                      title: 'أوقات الصلاة',
                                      color: theme.colorScheme.primary,
                                      onTap: () {
                                        HapticFeedback.mediumImpact();
                                        navigateTo(
                                          context,
                                          const PrayerTimesScreen(),
                                          type:
                                              PageTransitionType
                                                  .rightToLeftWithFade,
                                          duration: const Duration(
                                            milliseconds: 400,
                                          ),
                                        );
                                      },
                                    ),
                                  ),

                                  SizedBox(width: spacing),

                                  // المسبحة الإلكترونية
                                  Expanded(
                                    child: _buildFeatureGridItem(
                                      context: context,
                                      icon: Icons.radio_button_unchecked,
                                      title: 'المسبحة',
                                      color: theme.colorScheme.primary,
                                      onTap: () {
                                        HapticFeedback.mediumImpact();
                                        navigateTo(
                                          context,
                                          const TasbihScreen(),
                                          type:
                                              PageTransitionType
                                                  .rightToLeftWithFade,
                                          duration: const Duration(
                                            milliseconds: 400,
                                          ),
                                        );
                                      },
                                    ),
                                  ),

                                  SizedBox(width: spacing),

                                  // أسماء الله الحسنى
                                  Expanded(
                                    child: _buildFeatureGridItem(
                                      context: context,
                                      icon: Icons.star,
                                      title: 'أسماء الله الحسنى',
                                      color: theme.colorScheme.primary,
                                      onTap: () {
                                        HapticFeedback.mediumImpact();
                                        navigateTo(
                                          context,
                                          const AllahNamesScreen(),
                                          type:
                                              PageTransitionType
                                                  .rightToLeftWithFade,
                                          duration: const Duration(
                                            milliseconds: 400,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),

                              SizedBox(height: spacing),

                              // الصف الثاني: القبلة، الأدعية، الإحصائيات
                              Row(
                                children: [
                                  // القبلة
                                  Expanded(
                                    child: _buildFeatureGridItem(
                                      context: context,
                                      icon: Icons.explore,
                                      title: 'القبلة',
                                      color: theme.colorScheme.primary,
                                      onTap: () {
                                        HapticFeedback.mediumImpact();
                                        navigateTo(
                                          context,
                                          const QiblaScreen(),
                                          type:
                                              PageTransitionType
                                                  .rightToLeftWithFade,
                                          duration: const Duration(
                                            milliseconds: 400,
                                          ),
                                        );
                                      },
                                    ),
                                  ),

                                  SizedBox(width: spacing),

                                  // الأدعية
                                  Expanded(
                                    child: _buildFeatureGridItem(
                                      context: context,
                                      icon: Icons.volunteer_activism,
                                      title: 'الأدعية',
                                      color: theme.colorScheme.primary,
                                      onTap: () {
                                        HapticFeedback.mediumImpact();
                                        navigateTo(
                                          context,
                                          const DuaaScreen(),
                                          type:
                                              PageTransitionType
                                                  .rightToLeftWithFade,
                                          duration: const Duration(
                                            milliseconds: 400,
                                          ),
                                        );
                                      },
                                    ),
                                  ),

                                  SizedBox(width: spacing),

                                  // الإحصائيات والتتبع
                                  Expanded(
                                    child: _buildFeatureGridItem(
                                      context: context,
                                      icon: Icons.bar_chart,
                                      title: 'الإحصائيات والتتبع',
                                      color: theme.colorScheme.primary,
                                      onTap: () {
                                        HapticFeedback.mediumImpact();
                                        navigateTo(
                                          context,
                                          const StatisticsScreen(),
                                          type:
                                              PageTransitionType
                                                  .rightToLeftWithFade,
                                          duration: const Duration(
                                            milliseconds: 400,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),

                              SizedBox(height: spacing),

                              // الصف الثالث: رمضان والإعدادات
                              Row(
                                children: [
                                  // مساحة فارغة
                                  const Expanded(child: SizedBox()),

                                  // رمضان
                                  Expanded(
                                    child: _buildRamadanFeatureItem(context),
                                  ),

                                  SizedBox(width: spacing),

                                  // الإعدادات
                                  Expanded(
                                    child: _buildFeatureGridItem(
                                      context: context,
                                      icon: Icons.settings_rounded,
                                      title: 'الإعدادات',
                                      color: theme.colorScheme.primary,
                                      onTap: () {
                                        HapticFeedback.mediumImpact();
                                        navigateTo(
                                          context,
                                          const SettingsScreen(),
                                          type:
                                              PageTransitionType
                                                  .rightToLeftWithFade,
                                          duration: const Duration(
                                            milliseconds: 400,
                                          ),
                                        );
                                      },
                                    ),
                                  ),

                                  // مساحة فارغة
                                  const Expanded(child: SizedBox()),
                                ],
                              ),
                            ],
                          );
                        },
                      ),
                    ),

                    // مساحة إضافية في الأسفل
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // تم إزالة دالة _buildFeatureListItem لأنها لم تعد مستخدمة

  /// بناء عنصر رمضان المخصص
  Widget _buildRamadanFeatureItem(BuildContext context) {
    final ramadanService = RamadanService();
    final ramadanInfo = ramadanService.getCurrentRamadanInfo();

    // تحديد اللون والأيقونة بناءً على حالة رمضان
    final isRamadan = ramadanInfo.isCurrentlyRamadan;
    final color = isRamadan ? RamadanColors.gold : RamadanColors.darkGreen;
    final icon =
        isRamadan ? Icons.wb_incandescent : Icons.wb_incandescent_outlined;
    final title = isRamadan ? 'رمضان مبارك' : 'شهر رمضان';

    return _buildFeatureGridItem(
      context: context,
      icon: icon,
      title: title,
      color: color,
      onTap: () {
        HapticFeedback.mediumImpact();
        navigateTo(
          context,
          const RamadanScreen(),
          type: PageTransitionType.rightToLeftWithFade,
          duration: const Duration(milliseconds: 400),
        );
      },
    );
  }

  /// بناء عنصر ميزة في الشبكة
  Widget _buildFeatureGridItem({
    required BuildContext context,
    IconData? icon,
    String? iconPath,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // الحصول على حجم الشاشة
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // تحديد حجم الأيقونة والخط بناءً على حجم الشاشة
    double iconSize;
    double fontSize;
    double padding;

    if (screenWidth < 300) {
      // شاشات صغيرة جداً
      iconSize = 20;
      fontSize = 11;
      padding = 6;
    } else if (screenWidth < 360) {
      // شاشات صغيرة
      iconSize = 22;
      fontSize = 12;
      padding = 8;
    } else {
      // شاشات متوسطة وكبيرة
      iconSize = 24;
      fontSize = 13;
      padding = 10;
    }

    // التأكد من وجود أيقونة واحدة على الأقل
    assert(icon != null || iconPath != null, 'يجب توفير إما icon أو iconPath');

    // حساب ارتفاع العنصر بناءً على ارتفاع الشاشة
    final itemHeight = screenHeight * 0.15; // 15% من ارتفاع الشاشة

    return SizedBox(
      height: itemHeight,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Ink(
            decoration: BoxDecoration(
              color: theme.cardColor, // استخدام لون البطاقة من الثيم
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color:
                      isDarkMode
                          ? Colors.black.withAlpha(40) // ظل مثل تويتر
                          : Colors.black.withAlpha(13),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color:
                    isDarkMode
                        ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                        : color.withAlpha(51),
                width: 1,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(padding),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize:
                    MainAxisSize.min, // تقليل حجم العمود للحد الأدنى المطلوب
                children: [
                  // أيقونة الميزة
                  Container(
                    padding: EdgeInsets.all(padding),
                    decoration: BoxDecoration(
                      color: color.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child:
                        iconPath != null
                            ? SvgPicture.asset(
                              iconPath,
                              width: iconSize,
                              height: iconSize,
                              colorFilter: ColorFilter.mode(
                                color,
                                BlendMode.srcIn,
                              ),
                            )
                            : Icon(icon, color: color, size: iconSize),
                  ),

                  SizedBox(
                    height: padding / 2,
                  ), // تقليل المسافة بين الأيقونة والنص
                  // عنوان الميزة
                  Flexible(
                    // استخدام Flexible لضمان تناسب النص مع المساحة المتاحة
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: fontSize,
                        fontWeight: FontWeight.bold,
                        color:
                            isDarkMode
                                ? Colors.white
                                : theme.colorScheme.onSurface,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
