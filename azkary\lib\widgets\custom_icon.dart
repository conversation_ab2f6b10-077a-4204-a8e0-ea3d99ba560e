import 'package:flutter/material.dart';

/// مكون أيقونة مخصصة للتطبيق
class CustomIcon extends StatelessWidget {
  final String iconName;
  final double size;
  final Color? color;
  final bool isSelected;

  const CustomIcon({
    super.key,
    required this.iconName,
    this.size = 24.0,
    this.color,
    this.isSelected = false,
  });

  // تحويل اسم الأيقونة إلى IconData
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'mosque':
        return Icons.mosque; // أيقونة المسجد للأذكار
      case 'quran':
        return Icons.menu_book; // أيقونة الكتاب للقرآن
      case 'heart':
        return Icons.favorite; // أيقونة القلب للمفضلة
      case 'menu_dots':
        return Icons.more_horiz; // أيقونة النقاط للمزيد
      case 'home':
        return Icons.home;
      case 'favorite':
        return Icons.favorite;
      case 'more':
        return Icons.more_horiz;
      case 'quran_icon':
        return Icons.menu_book;
      case 'settings':
        return Icons.settings;
      case 'tasbih':
        return Icons.circle;
      default:
        return Icons.help_outline; // أيقونة افتراضية
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final iconColor =
        color ?? (isSelected ? theme.colorScheme.primary : Colors.grey);

    return Icon(_getIconData(iconName), size: size, color: iconColor);
  }
}

/// مكون أيقونة شريط التنقل السفلي
class BottomNavIcon extends StatelessWidget {
  final String iconName;
  final bool isSelected;
  final String label;

  const BottomNavIcon({
    super.key,
    required this.iconName,
    required this.isSelected,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color:
            isSelected
                ? (isDarkMode
                    ? theme.colorScheme.primary.withAlpha(
                      38,
                    ) // 0.15 * 255 = ~38
                    : theme.colorScheme.primary.withAlpha(
                      20,
                    ) // 0.08 * 255 = ~20
                    )
                : Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        boxShadow:
            isSelected
                ? [
                  BoxShadow(
                    color: theme.colorScheme.primary.withAlpha(
                      26,
                    ), // 0.1 * 255 = ~26
                    blurRadius: 8,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ]
                : null,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونة ثابتة بدون تأثيرات حركية
          CustomIcon(
            iconName: iconName,
            size: isSelected ? 24 : 22,
            isSelected: isSelected,
          ),
          const SizedBox(height: 4),
          // نص بدون تأثير حركي
          Text(
            label,
            style: TextStyle(
              fontSize: isSelected ? 13 : 12,
              color: isSelected ? theme.colorScheme.primary : Colors.grey,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
