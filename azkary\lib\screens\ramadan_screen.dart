import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/ramadan_model.dart';
import '../services/ramadan_service.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/enhanced_card.dart';
import '../widgets/enhanced_animations.dart';
import '../widgets/islamic_pattern.dart';
import 'ramadan_duas_screen.dart';
import 'ramadan_tasbihs_screen.dart';
import 'ramadan_info_screen.dart';

/// شاشة رمضان الرئيسية
class RamadanScreen extends StatefulWidget {
  const RamadanScreen({super.key});

  @override
  State<RamadanScreen> createState() => _RamadanScreenState();
}

class _RamadanScreenState extends State<RamadanScreen>
    with TickerProviderStateMixin {
  final RamadanService _ramadanService = RamadanService();
  late RamadanInfo _ramadanInfo;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _ramadanInfo = _ramadanService.getCurrentRamadanInfo();

    // إعداد الرسوم المتحركة للنبضة
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // بدء الرسوم المتحركة إذا كان رمضان جارياً
    if (_ramadanInfo.isCurrentlyRamadan) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // استخدام ألوان السمة مع الحفاظ على الطابع الرمضاني
    final primaryColor = theme.colorScheme.primary;
    final ramadanPrimary =
        _ramadanInfo.isCurrentlyRamadan
            ? Color.lerp(primaryColor, RamadanColors.gold, 0.3)!
            : Color.lerp(primaryColor, RamadanColors.darkGreen, 0.3)!;
    final ramadanSecondary =
        isDarkMode
            ? Color.lerp(primaryColor, RamadanColors.darkGreen, 0.2)!
            : Color.lerp(primaryColor, RamadanColors.lightGold, 0.2)!;

    return Scaffold(
      appBar: const CustomAppBar(title: 'شهر رمضان المبارك'),
      body: Stack(
        children: [
          // خلفية رمضانية مع ألوان السمة
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    ramadanSecondary.withValues(alpha: 0.1),
                    theme.scaffoldBackgroundColor,
                  ],
                ),
              ),
            ),
          ),

          // نمط إسلامي في الخلفية
          Positioned.fill(
            child: Opacity(
              opacity: 0.03,
              child: IslamicPattern(color: ramadanPrimary),
            ),
          ),

          // المحتوى الرئيسي
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // بطاقة معلومات رمضان
                _buildRamadanInfoCard(),
                const SizedBox(height: 20),

                // العداد التنازلي أو أيام رمضان المتبقية
                _buildCountdownCard(),
                const SizedBox(height: 20),

                // قائمة الميزات الرمضانية
                _buildFeaturesGrid(),
                const SizedBox(height: 20),

                // اقتباس رمضاني
                _buildRamadanQuote(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة معلومات رمضان
  Widget _buildRamadanInfoCard() {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    // تدرج يجمع بين ألوان السمة والطابع الرمضاني
    final cardGradient =
        _ramadanInfo.isCurrentlyRamadan
            ? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color.lerp(primaryColor, RamadanColors.gold, 0.4)!,
                Color.lerp(primaryColor, RamadanColors.darkGreen, 0.4)!,
              ],
            )
            : LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color.lerp(primaryColor, RamadanColors.lightGold, 0.3)!,
                Color.lerp(primaryColor, RamadanColors.emerald, 0.3)!,
              ],
            );

    return FadeInAnimation(
      delay: const Duration(milliseconds: 200),
      child: EnhancedCard(
        onTap: () {
          HapticFeedback.lightImpact();
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const RamadanInfoScreen()),
          );
        },
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: cardGradient,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              // أيقونة الهلال
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Icon(
                      RamadanIcons.crescent,
                      size: 48,
                      color: Colors.white,
                    ),
                  );
                },
              ),
              const SizedBox(height: 12),

              // عنوان
              Text(
                _ramadanInfo.isCurrentlyRamadan
                    ? 'رمضان مبارك!'
                    : 'رمضان ${_ramadanInfo.year}',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              // التواريخ
              Text(
                '${_formatDate(_ramadanInfo.startDate)} - ${_formatDate(_ramadanInfo.endDate)}',
                style: const TextStyle(fontSize: 16, color: Colors.white70),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة العداد التنازلي
  Widget _buildCountdownCard() {
    return FadeInAnimation(
      delay: const Duration(milliseconds: 400),
      child: EnhancedCard(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(
                RamadanIcons.timer,
                size: 40,
                color:
                    _ramadanInfo.isCurrentlyRamadan
                        ? RamadanColors.gold
                        : RamadanColors.darkGreen,
              ),
              const SizedBox(height: 12),

              Text(
                _ramadanInfo.isCurrentlyRamadan
                    ? 'أيام متبقية في رمضان'
                    : 'العداد التنازلي لرمضان',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // العداد
              _buildCountdownDisplay(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عرض العداد
  Widget _buildCountdownDisplay() {
    if (_ramadanInfo.isCurrentlyRamadan) {
      return Text(
        '${_ramadanInfo.daysRemaining} يوم',
        style: TextStyle(
          fontSize: 36,
          fontWeight: FontWeight.bold,
          color: RamadanColors.gold,
        ),
        textAlign: TextAlign.center,
      );
    } else {
      final days = _ramadanInfo.timeUntilRamadan.inDays;
      final hours = _ramadanInfo.timeUntilRamadan.inHours % 24;

      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildCountdownItem('يوم', days.toString()),
              _buildCountdownItem('ساعة', hours.toString()),
            ],
          ),
        ],
      );
    }
  }

  /// بناء عنصر العداد
  Widget _buildCountdownItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: RamadanColors.darkGreen,
          ),
        ),
        Text(label, style: Theme.of(context).textTheme.bodyMedium),
      ],
    );
  }

  /// بناء شبكة الميزات
  Widget _buildFeaturesGrid() {
    final features = [
      {
        'title': 'الأدعية الرمضانية',
        'subtitle': 'أدعية الإفطار والسحور وليلة القدر',
        'icon': RamadanIcons.prayer,
        'color': RamadanColors.gold,
        'screen': const RamadanDuasScreen(),
      },
      {
        'title': 'التسبيحات الرمضانية',
        'subtitle': 'أذكار ما بعد الإفطار والتراويح',
        'icon': RamadanIcons.peace,
        'color': RamadanColors.darkGreen,
        'screen': const RamadanTasbihsScreen(),
      },
      {
        'title': 'معلومات رمضان',
        'subtitle': 'تفاصيل الشهر الكريم وأوقاته',
        'icon': RamadanIcons.calendar,
        'color': RamadanColors.emerald,
        'screen': const RamadanInfoScreen(),
      },
    ];

    return Column(
      children:
          features.asMap().entries.map((entry) {
            final index = entry.key;
            final feature = entry.value;

            return FadeInAnimation(
              delay: Duration(milliseconds: 600 + (index * 200)),
              child: Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildFeatureCard(feature),
              ),
            );
          }).toList(),
    );
  }

  /// بناء بطاقة ميزة
  Widget _buildFeatureCard(Map<String, dynamic> feature) {
    return EnhancedCard(
      onTap: () {
        HapticFeedback.lightImpact();
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => feature['screen']),
        );
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // أيقونة الميزة
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: (feature['color'] as Color).withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                feature['icon'] as IconData,
                color: feature['color'] as Color,
                size: 28,
              ),
            ),
            const SizedBox(width: 16),

            // معلومات الميزة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    feature['title'] as String,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    feature['subtitle'] as String,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),

            // سهم
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء اقتباس رمضاني
  Widget _buildRamadanQuote() {
    return FadeInAnimation(
      delay: const Duration(milliseconds: 1200),
      child: EnhancedCard(
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                RamadanColors.cream.withValues(alpha: 0.3),
                RamadanColors.lightGold.withValues(alpha: 0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              Icon(RamadanIcons.book, color: RamadanColors.bronze, size: 32),
              const SizedBox(height: 12),
              Text(
                '"شهر رمضان الذي أنزل فيه القرآن هدى للناس وبينات من الهدى والفرقان"',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontStyle: FontStyle.italic,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'سورة البقرة - آية 185',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: RamadanColors.bronze),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return '${date.day} ${months[date.month - 1]}';
  }
}
