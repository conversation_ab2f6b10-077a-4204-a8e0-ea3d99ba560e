import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/azkar_provider.dart';
import '../widgets/zikr_list_item.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_pattern.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  @override
  void initState() {
    super.initState();
    // Load favorites when screen initializes
    // Favorites are loaded automatically in the provider constructor
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'المفضلة',
        leading: Container(), // إزالة زر الرجوع
      ),
      body: Stack(
        children: [
          // خلفية إسلامية
          Positioned.fill(
            child: Opacity(
              opacity: 0.05,
              child: IslamicPattern(color: theme.colorScheme.primary),
            ),
          ),

          // محتوى الصفحة
          Consumer<AzkarProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (provider.favoriteAzkar.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.favorite_border,
                        size: 64,
                        color: Theme.of(context).colorScheme.primary.withAlpha(
                          128,
                        ), // 0.5 opacity = 128 alpha
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'لا توجد أذكار في المفضلة',
                        style: TextStyle(fontSize: 18),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'يمكنك إضافة الأذكار إلى المفضلة بالضغط على أيقونة القلب',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(16.0),
                itemCount: provider.favoriteAzkar.length,
                itemBuilder: (context, index) {
                  final zikr = provider.favoriteAzkar[index];
                  return ZikrListItem(
                    zikr: zikr,
                    onTap: () {
                      // لا نقوم بالانتقال إلى صفحة التفاصيل، بل نترك التوسيع/الطي للعنصر نفسه
                    },
                    onFavoriteToggle: () {
                      provider.toggleFavorite(zikr);
                    },
                    onCounterIncrement: (zikr, count) {
                      provider.updateZikrCount(zikr, count);
                    },
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }
}
