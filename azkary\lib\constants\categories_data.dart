import '../models/azkar_model.dart';

/// بيانات فئات الأذكار
final List<Category> categoriesData = [
  Category(
    id: 1,
    name: 'أذكار الصباح',
    icon: 'assets/icons/morning.png',
    count: 31,
    description: 'أذكار الصباح',
  ),
  Category(
    id: 2,
    name: 'أذكار المساء',
    icon: 'assets/icons/evening.png',
    count: 31,
    description: 'أذكار المساء',
  ),
  Category(
    id: 3,
    name: 'أذكار النوم',
    icon: 'assets/icons/sleep.png',
    count: 15,
    description: 'أذكار النوم',
  ),
  Category(
    id: 4,
    name: 'أذكار الاستيقاظ',
    icon: 'assets/icons/wakeup.png',
    count: 8,
    description: 'أذكار الاستيقاظ',
  ),
  Category(
    id: 5,
    name: 'أذكار الصلاة',
    icon: 'assets/icons/prayer.png',
    count: 12,
    description: 'أذكار الصلاة',
  ),
  Category(
    id: 6,
    name: 'أذك<PERSON><PERSON> المسجد',
    icon: 'assets/icons/mosque.png',
    count: 10,
    description: 'أذكار المسجد',
  ),
  Category(
    id: 7,
    name: 'أذكار الوضوء',
    icon: 'assets/icons/wudu.png',
    count: 6,
    description: 'أذكار الوضوء',
  ),
  Category(
    id: 8,
    name: 'أذكار الطعام',
    icon: 'assets/icons/food.png',
    count: 8,
    description: 'أذكار الطعام',
  ),
  Category(
    id: 9,
    name: 'أذكار السفر',
    icon: 'assets/icons/travel.png',
    count: 10,
    description: 'أذكار السفر',
  ),
  Category(
    id: 10,
    name: 'أذكار متنوعة',
    icon: 'assets/icons/general.png',
    count: 20,
    description: 'أذكار متنوعة',
  ),
];
