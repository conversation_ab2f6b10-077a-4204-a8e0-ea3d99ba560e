import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/ramadan_model.dart';
import '../services/ramadan_service.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/enhanced_card.dart';
import '../widgets/enhanced_animations.dart';
import '../widgets/islamic_pattern.dart';

/// شاشة الأدعية الرمضانية
class RamadanDuasScreen extends StatefulWidget {
  const RamadanDuasScreen({super.key});

  @override
  State<RamadanDuasScreen> createState() => _RamadanDuasScreenState();
}

class _RamadanDuasScreenState extends State<RamadanDuasScreen>
    with TickerProviderStateMixin {
  final RamadanService _ramadanService = RamadanService();
  late List<RamadanDua> _duas;
  String _selectedCategory = 'all';
  late AnimationController _sparkleController;

  @override
  void initState() {
    super.initState();
    _duas = _ramadanService.getRamadanDuas();
    
    // إعداد الرسوم المتحركة للتألق
    _sparkleController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _sparkleController.repeat();
  }

  @override
  void dispose() {
    _sparkleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final filteredDuas = _getFilteredDuas();

    return Scaffold(
      appBar: const CustomAppBar(title: 'الأدعية الرمضانية'),
      body: Stack(
        children: [
          // خلفية رمضانية
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    RamadanColors.lightGold.withValues(alpha: 0.1),
                    theme.scaffoldBackgroundColor,
                  ],
                ),
              ),
            ),
          ),

          // نمط إسلامي
          Positioned.fill(
            child: Opacity(
              opacity: 0.03,
              child: IslamicPattern(color: RamadanColors.gold),
            ),
          ),

          // المحتوى
          Column(
            children: [
              // شريط التصفية
              _buildCategoryFilter(),
              
              // قائمة الأدعية
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredDuas.length,
                  itemBuilder: (context, index) {
                    return FadeInAnimation(
                      delay: Duration(milliseconds: index * 100),
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: _buildDuaCard(filteredDuas[index]),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء شريط تصفية الفئات
  Widget _buildCategoryFilter() {
    final categories = [
      {'id': 'all', 'name': 'الكل'},
      {'id': 'iftar', 'name': 'الإفطار'},
      {'id': 'suhoor', 'name': 'السحور'},
      {'id': 'laylatAlQadr', 'name': 'ليلة القدر'},
      {'id': 'lastTenDays', 'name': 'العشر الأواخر'},
      {'id': 'accepted', 'name': 'المستجابة'},
    ];

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = _selectedCategory == category['id'];
          
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(category['name']!),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category['id']!;
                });
                HapticFeedback.lightImpact();
              },
              selectedColor: RamadanColors.gold.withValues(alpha: 0.3),
              checkmarkColor: RamadanColors.darkGreen,
            ),
          );
        },
      ),
    );
  }

  /// بناء بطاقة الدعاء
  Widget _buildDuaCard(RamadanDua dua) {
    return EnhancedCard(
      onTap: () => _showDuaDetails(dua),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              dua.color.withValues(alpha: 0.1),
              Colors.transparent,
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: dua.color.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    dua.icon,
                    color: dua.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        dua.title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: dua.color,
                        ),
                      ),
                      if (dua.source != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'المصدر: ${dua.source}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ],
                  ),
                ),
                AnimatedBuilder(
                  animation: _sparkleController,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _sparkleController.value * 2 * 3.14159,
                      child: Icon(
                        RamadanIcons.star,
                        color: dua.color.withValues(alpha: 0.5),
                        size: 20,
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),

            // النص العربي
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: dua.color.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Text(
                dua.arabicText,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  height: 1.8,
                  fontFamily: 'Uthmani',
                ),
                textAlign: TextAlign.center,
                textDirection: TextDirection.rtl,
              ),
            ),
            const SizedBox(height: 12),

            // الترجمة
            Text(
              dua.translation,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),

            if (dua.benefits != null) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: dua.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: dua.color,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        dua.benefits!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: dua.color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// عرض تفاصيل الدعاء
  void _showDuaDetails(RamadanDua dua) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          dua.title,
          textAlign: TextAlign.center,
          style: TextStyle(color: dua.color),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // النص العربي
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: dua.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  dua.arabicText,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    height: 1.8,
                    fontFamily: 'Uthmani',
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection.rtl,
                ),
              ),
              const SizedBox(height: 16),

              // النطق
              Text(
                dua.transliteration,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // الترجمة
              Text(
                dua.translation,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),

              if (dua.source != null) ...[
                const SizedBox(height: 12),
                Text(
                  'المصدر: ${dua.source}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: dua.color,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: dua.arabicText));
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم نسخ الدعاء')),
              );
            },
            icon: const Icon(Icons.copy),
            label: const Text('نسخ'),
            style: ElevatedButton.styleFrom(
              backgroundColor: dua.color,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// تصفية الأدعية حسب الفئة المختارة
  List<RamadanDua> _getFilteredDuas() {
    if (_selectedCategory == 'all') {
      return _duas;
    }
    return _duas.where((dua) => dua.category == _selectedCategory).toList();
  }
}
