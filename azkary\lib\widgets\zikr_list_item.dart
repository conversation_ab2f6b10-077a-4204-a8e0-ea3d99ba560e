import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import '../models/azkar_model.dart';
import '../widgets/mini_zikr_counter.dart';
import '../widgets/animated_widgets.dart';
import '../utils/logger.dart';

// تحويل إلى StatefulWidget للتمكن من توسيع/طي العنصر
class ZikrListItem extends StatefulWidget {
  final Zikr zikr;
  final Function()? onTap;
  final Function()? onFavoriteToggle;
  final Function(Zikr, int)? onCounterIncrement;
  final Function()? onEdit; // إضافة دالة للتعديل
  final bool showCategory; // إضافة خيار لعرض التصنيف
  final bool showEditButton; // إضافة خيار لعرض زر التعديل

  const ZikrListItem({
    super.key,
    required this.zikr,
    this.onTap,
    this.onFavoriteToggle,
    this.onCounterIncrement,
    this.onEdit,
    this.showCategory = false, // افتراضياً لا يتم عرض التصنيف
    this.showEditButton = false, // افتراضياً لا يتم عرض زر التعديل
  });

  @override
  State<ZikrListItem> createState() => _ZikrListItemState();
}

class _ZikrListItemState extends State<ZikrListItem> {
  // حالة توسيع/طي العنصر
  bool _isExpanded = false;

  // دالة لإعادة ضبط العداد
  void _resetCounter() {
    if (widget.zikr.currentCount > 0) {
      // تحديث القيمة محلياً أولاً للاستجابة الفورية
      setState(() {
        widget.zikr.currentCount = 0;
      });

      // تنفيذ إعادة ضبط العداد في قاعدة البيانات إذا كانت الدالة متوفرة
      if (widget.onCounterIncrement != null) {
        widget.onCounterIncrement!(widget.zikr, 0);
      }
    }
  }

  // دالة لنسخ الذكر إلى الحافظة
  void _copyZikrToClipboard(Zikr zikr) {
    final textToCopy = '${zikr.text}\n\nالمصدر: ${zikr.source}';
    Clipboard.setData(ClipboardData(text: textToCopy)).then((_) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ الذكر إلى الحافظة'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    });
  }

  // دالة لمشاركة الذكر
  void _shareZikr(Zikr zikr) {
    final textToShare =
        '${zikr.text}\n\nالمصدر: ${zikr.source}\n\nمن تطبيق أذكاري';
    // استخدام طريقة المشاركة
    try {
      share_plus.SharePlus.instance.share(
        share_plus.ShareParams(text: textToShare),
      );
      AppLogger.info(
        'تمت مشاركة الذكر: ${zikr.text.substring(0, zikr.text.length > 30 ? 30 : zikr.text.length)}...',
      );
    } catch (e) {
      AppLogger.error('خطأ في المشاركة: $e');
    }
  }

  // دالة لبناء زر الإجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return AnimatedPressableButton(
      onPressed: onTap,
      color: Colors.transparent,
      splashColor: theme.colorScheme.primary.withValues(
        red: theme.colorScheme.primary.r.toDouble(),
        green: theme.colorScheme.primary.g.toDouble(),
        blue: theme.colorScheme.primary.b.toDouble(),
        alpha: 0.12, // 30/255 ≈ 0.12
      ),
      highlightColor: theme.colorScheme.primary.withValues(
        red: theme.colorScheme.primary.r.toDouble(),
        green: theme.colorScheme.primary.g.toDouble(),
        blue: theme.colorScheme.primary.b.toDouble(),
        alpha: 0.08, // 20/255 ≈ 0.08
      ),
      borderRadius: BorderRadius.circular(8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: theme.colorScheme.primary),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              fontSize:
                  theme
                      .textTheme
                      .bodySmall
                      ?.fontSize, // استخدام حجم الخط من السمة
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isCompleted = widget.zikr.currentCount >= widget.zikr.count;

    // إذا كان الذكر مكتملاً، نقوم بتشغيل حركة انتقالية

    // استخراج الألوان من السمة
    final primaryColor = theme.colorScheme.primary;
    final onSurfaceColor = theme.colorScheme.onSurface;
    final onSurfaceMuted = onSurfaceColor.withValues(
      red: onSurfaceColor.r.toDouble(),
      green: onSurfaceColor.g.toDouble(),
      blue: onSurfaceColor.b.toDouble(),
      alpha: 0.6, // 153/255 ≈ 0.6
    );
    final primaryMuted = primaryColor.withValues(
      red: primaryColor.r.toDouble(),
      green: primaryColor.g.toDouble(),
      blue: primaryColor.b.toDouble(),
      alpha: 0.1, // 26/255 ≈ 0.1
    );

    // استخدام TextStyle مخزنة مسبقًا لتحسين الأداء مع استخدام حجم الخط من السمة

    final countValueStyle = TextStyle(
      fontSize:
          theme.textTheme.bodySmall?.fontSize, // استخدام حجم الخط من السمة
      fontWeight: FontWeight.bold,
      color: isCompleted ? Colors.white : primaryColor,
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isCompleted ? 6 : 4, // زيادة ارتفاع الظل للأذكار المكتملة
      color:
          theme.brightness == Brightness.dark
              ? isCompleted
                  ? const Color(
                    0xFF2E3A2F,
                  ) // لون داكن مائل للأخضر للأذكار المكتملة
                  : theme
                      .cardColor // استخدام لون البطاقة من الثيم (تويتر)
              : isCompleted
              ? const Color(0xFFF8FFF9) // لون فاتح مائل للأخضر للأذكار المكتملة
              : Colors.white, // لون أبيض عادي
      shadowColor:
          theme.brightness == Brightness.dark
              ? Colors.black.withValues(
                red: 0,
                green: 0,
                blue: 0,
                alpha: 0.2, // ظل مثل تويتر
              )
              : isCompleted
              ? primaryColor.withValues(
                red: primaryColor.r.toDouble(),
                green: primaryColor.g.toDouble(),
                blue: primaryColor.b.toDouble(),
                alpha: 0.2,
              ) // ظل بلون أخضر خفيف للأذكار المكتملة
              : Colors.black.withValues(
                red: 0,
                green: 0,
                blue: 0,
                alpha: 0.16,
              ), // ظل عادي
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color:
              isCompleted
                  ? primaryColor.withValues(
                    red: primaryColor.r.toDouble(),
                    green: primaryColor.g.toDouble(),
                    blue: primaryColor.b.toDouble(),
                    alpha: 0.4, // زيادة وضوح البرواز للأذكار المكتملة
                  ) // لون البرواز عند اكتمال الذكر
                  : theme.brightness == Brightness.dark
                  ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                  : Colors.grey.withValues(
                    red: Colors.grey.r.toDouble(),
                    green: Colors.grey.g.toDouble(),
                    blue: Colors.grey.b.toDouble(),
                    alpha: 0.12, // 30/255 ≈ 0.12
                  ), // برواز رفيع ثابت
          width: isCompleted ? 1.5 : 1, // زيادة سمك البرواز للأذكار المكتملة
        ),
      ),
      child: InkWell(
        onTap:
            widget.onTap ??
            () {
              // إذا لم يتم توفير دالة onTap، نقوم بتوسيع/طي العنصر
              if (widget.zikr.fadl != null && widget.zikr.fadl!.isNotEmpty) {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              }
            },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: نص الذكر وزر المفضلة
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // نص الذكر مع تحسينات بصرية
                  Expanded(
                    child: Container(
                      decoration:
                          isCompleted
                              ? BoxDecoration(
                                border: Border(
                                  right: BorderSide(
                                    color: primaryColor,
                                    width: 3,
                                  ),
                                ),
                              )
                              : null,
                      padding:
                          isCompleted
                              ? const EdgeInsets.only(right: 8)
                              : EdgeInsets.zero,
                      child: Text(
                        widget.zikr.text,
                        style: TextStyle(
                          fontSize: theme.textTheme.bodyLarge?.fontSize,
                          color: onSurfaceColor,
                          height:
                              1.8, // زيادة المسافة بين السطور لتحسين القراءة
                          fontWeight:
                              isCompleted ? FontWeight.bold : FontWeight.normal,
                          letterSpacing: 0.2, // تحسين المسافة بين الحروف
                        ),
                        textDirection:
                            TextDirection
                                .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                        textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                        softWrap: true, // السماح بلف النص
                        overflow: TextOverflow.visible, // عدم قطع النص
                      ),
                    ),
                  ),
                  // زر المفضلة
                  AnimatedPressableButton(
                    onPressed: () {
                      // تحديث حالة المفضلة محلياً أولاً للاستجابة الفورية
                      setState(() {
                        // تغيير حالة المفضلة مؤقتاً في واجهة المستخدم
                        widget.zikr.isFavorite = !widget.zikr.isFavorite;
                      });
                      // استدعاء دالة تبديل المفضلة إذا كانت متوفرة
                      if (widget.onFavoriteToggle != null) {
                        widget.onFavoriteToggle!();
                      }
                    },
                    scale: 0.8,
                    duration: const Duration(milliseconds: 100),
                    padding: const EdgeInsets.all(8),
                    color: Colors.transparent,
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      transitionBuilder: (
                        Widget child,
                        Animation<double> animation,
                      ) {
                        return ScaleTransition(scale: animation, child: child);
                      },
                      child: Icon(
                        widget.zikr.isFavorite
                            ? Icons.favorite
                            : Icons.favorite_border,
                        key: ValueKey<bool>(widget.zikr.isFavorite),
                        color:
                            widget.zikr.isFavorite ? Colors.red : Colors.grey,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),

              // تم نقل أزرار توسيع/طي العنصر إلى ما بعد عدد التكرار
              const SizedBox(height: 12),

              // الصف الثاني: المصدر وعدد التكرار (وإضافة التصنيف إذا كان مطلوباً)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // المصدر مع تحسينات بصرية
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                theme.brightness == Brightness.dark
                                    ? Colors.black.withValues(
                                      red: 0,
                                      green: 0,
                                      blue: 0,
                                      alpha: 0.1,
                                    )
                                    : Colors.grey.withValues(
                                      red: 0,
                                      green: 0,
                                      blue: 0,
                                      alpha: 0.05,
                                    ),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.grey.withValues(
                                red: Colors.grey.r.toDouble(),
                                green: Colors.grey.g.toDouble(),
                                blue: Colors.grey.b.toDouble(),
                                alpha: 0.2,
                              ),
                              width: 0.5,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.menu_book_outlined,
                                size: 14,
                                color: onSurfaceMuted,
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  widget.zikr.source,
                                  style: TextStyle(
                                    fontSize:
                                        theme.textTheme.bodySmall?.fontSize,
                                    fontWeight: FontWeight.w500,
                                    color: onSurfaceMuted,
                                  ),
                                  softWrap: true,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // عدد التكرار مع تحسينات بصرية
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: isCompleted ? primaryColor : primaryMuted,
                          borderRadius: const BorderRadius.all(
                            Radius.circular(12),
                          ),
                          boxShadow:
                              isCompleted
                                  ? [
                                    BoxShadow(
                                      color: primaryColor.withValues(
                                        red: primaryColor.r.toDouble(),
                                        green: primaryColor.g.toDouble(),
                                        blue: primaryColor.b.toDouble(),
                                        alpha: 0.3,
                                      ),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ]
                                  : null,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.repeat,
                              size: 14,
                              color: isCompleted ? Colors.white : primaryColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${widget.zikr.count} مرة',
                              style: countValueStyle,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // إظهار التصنيف إذا كان مطلوباً
                  if (widget.showCategory && widget.zikr.category.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.secondary.withValues(
                            red: theme.colorScheme.secondary.r.toDouble(),
                            green: theme.colorScheme.secondary.g.toDouble(),
                            blue: theme.colorScheme.secondary.b.toDouble(),
                            alpha: 0.12, // 30/255 ≈ 0.12
                          ),
                          borderRadius: const BorderRadius.all(
                            Radius.circular(12),
                          ),
                        ),
                        child: Text(
                          'التصنيف: ${widget.zikr.category}',
                          style: TextStyle(
                            fontSize:
                                theme
                                    .textTheme
                                    .bodySmall
                                    ?.fontSize, // استخدام حجم الخط من السمة
                            color: theme.colorScheme.secondary,
                            fontWeight: FontWeight.w500,
                          ),
                          softWrap: true,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // أزرار توسيع/طي فضل الذكر مع تحسينات بصرية
              if (widget.zikr.fadl != null && widget.zikr.fadl!.isNotEmpty)
                Align(
                  alignment: Alignment.centerRight,
                  child: AnimatedPressableButton(
                    onPressed: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                      // إضافة تأثير اهتزاز خفيف عند النقر
                      HapticFeedback.lightImpact();
                    },
                    color:
                        _isExpanded
                            ? theme.colorScheme.secondary.withValues(
                              red: theme.colorScheme.secondary.r.toDouble(),
                              green: theme.colorScheme.secondary.g.toDouble(),
                              blue: theme.colorScheme.secondary.b.toDouble(),
                              alpha: 0.15,
                            )
                            : theme.colorScheme.secondary.withValues(
                              red: theme.colorScheme.secondary.r.toDouble(),
                              green: theme.colorScheme.secondary.g.toDouble(),
                              blue: theme.colorScheme.secondary.b.toDouble(),
                              alpha: 0.08,
                            ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      transitionBuilder: (
                        Widget child,
                        Animation<double> animation,
                      ) {
                        return FadeTransition(
                          opacity: animation,
                          child: SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(0, 0.2),
                              end: Offset.zero,
                            ).animate(animation),
                            child: child,
                          ),
                        );
                      },
                      child: Row(
                        key: ValueKey<bool>(_isExpanded),
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                            size: 16,
                            color: theme.colorScheme.secondary,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            _isExpanded ? 'إخفاء فضل الذكر' : 'عرض فضل الذكر',
                            style: TextStyle(
                              fontSize: theme.textTheme.bodySmall?.fontSize,
                              fontWeight: FontWeight.w500,
                              color: theme.colorScheme.secondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 12),

              // زر التسبيح مع العداد بداخله
              Align(
                alignment: Alignment.centerRight,
                child: MiniZikrCounter(
                  currentCount: widget.zikr.currentCount,
                  totalCount: widget.zikr.count,
                  onIncrement: () {
                    // زيادة العداد بمقدار 1 إذا لم يكتمل العد
                    if (widget.zikr.currentCount < widget.zikr.count) {
                      // تنفيذ زيادة العداد مباشرة
                      final newCount = widget.zikr.currentCount + 1;

                      // تحديث القيمة محلياً أولاً للاستجابة الفورية
                      setState(() {
                        widget.zikr.currentCount = newCount;
                      });

                      // ثم تحديث القيمة في قاعدة البيانات إذا كانت الدالة متوفرة
                      if (widget.onCounterIncrement != null) {
                        widget.onCounterIncrement!(widget.zikr, newCount);
                      }
                    }
                  },
                  onReset: _resetCounter, // إضافة وظيفة إعادة ضبط العداد
                ),
              ),

              // أزرار النسخ والمشاركة والتعديل مع تحسينات بصرية
              Container(
                margin: const EdgeInsets.only(top: 16.0),
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 12,
                ),
                decoration: BoxDecoration(
                  color:
                      theme.brightness == Brightness.dark
                          ? Colors.black.withValues(
                            red: 0,
                            green: 0,
                            blue: 0,
                            alpha: 0.2,
                          )
                          : Colors.grey.withValues(
                            red: 0,
                            green: 0,
                            blue: 0,
                            alpha: 0.05,
                          ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // زر التعديل (يظهر فقط للأذكار الشخصية أو إذا كان مطلوباً)
                    if ((widget.zikr.isCustom || widget.showEditButton) &&
                        widget.onEdit != null)
                      Row(
                        children: [
                          _buildActionButton(
                            icon: Icons.edit_outlined,
                            label: 'تعديل',
                            onTap: widget.onEdit!,
                          ),
                          const SizedBox(width: 16),
                        ],
                      ),
                    _buildActionButton(
                      icon: Icons.copy_outlined,
                      label: 'نسخ',
                      onTap: () => _copyZikrToClipboard(widget.zikr),
                    ),
                    const SizedBox(width: 16),
                    _buildActionButton(
                      icon: Icons.share_outlined,
                      label: 'مشاركة',
                      onTap: () => _shareZikr(widget.zikr),
                    ),
                  ],
                ),
              ),

              // عرض فضل الذكر إذا كان متاحًا وكان العنصر موسعًا
              if (_isExpanded &&
                  widget.zikr.fadl != null &&
                  widget.zikr.fadl!.isNotEmpty)
                AnimatedAppearance(
                  offset: const Offset(0, 20),
                  duration: const Duration(milliseconds: 300),
                  child: Container(
                    margin: const EdgeInsets.only(top: 20.0),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        colors: [
                          theme.colorScheme.secondary.withValues(
                            red: theme.colorScheme.secondary.r.toDouble(),
                            green: theme.colorScheme.secondary.g.toDouble(),
                            blue: theme.colorScheme.secondary.b.toDouble(),
                            alpha: 0.12,
                          ),
                          theme.colorScheme.secondary.withValues(
                            red: theme.colorScheme.secondary.r.toDouble(),
                            green: theme.colorScheme.secondary.g.toDouble(),
                            blue: theme.colorScheme.secondary.b.toDouble(),
                            alpha: 0.05,
                          ),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: theme.colorScheme.secondary.withValues(
                            red: theme.colorScheme.secondary.r.toDouble(),
                            green: theme.colorScheme.secondary.g.toDouble(),
                            blue: theme.colorScheme.secondary.b.toDouble(),
                            alpha: 0.1,
                          ),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: theme.colorScheme.secondary.withValues(
                          red: theme.colorScheme.secondary.r.toDouble(),
                          green: theme.colorScheme.secondary.g.toDouble(),
                          blue: theme.colorScheme.secondary.b.toDouble(),
                          alpha: 0.2,
                        ),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // عنوان فضل الذكر مع أيقونة
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.secondary.withValues(
                              red: theme.colorScheme.secondary.r.toDouble(),
                              green: theme.colorScheme.secondary.g.toDouble(),
                              blue: theme.colorScheme.secondary.b.toDouble(),
                              alpha: 0.15,
                            ),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.star_rounded,
                                size: 18,
                                color: theme.colorScheme.secondary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'فضل الذكر',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize:
                                      theme.textTheme.titleMedium?.fontSize,
                                  color: theme.colorScheme.secondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 12),
                        // نص فضل الذكر
                        Container(
                          width:
                              double
                                  .infinity, // تحديد العرض ليأخذ كامل المساحة المتاحة
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color:
                                theme.brightness == Brightness.dark
                                    ? Colors.black.withValues(
                                      red: 0,
                                      green: 0,
                                      blue: 0,
                                      alpha: 0.2,
                                    )
                                    : const Color(0xFFF5F5DC).withValues(
                                      // لون بيج فاتح للوضع الفاتح
                                      red: 245,
                                      green: 245,
                                      blue: 220,
                                      alpha: 1.0,
                                    ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            widget.zikr.fadl!,
                            style: TextStyle(
                              fontSize: theme.textTheme.bodyMedium?.fontSize,
                              height: 1.8,
                              color:
                                  theme.brightness == Brightness.dark
                                      ? Colors.white
                                      : const Color(
                                        0xFF3E2723,
                                      ), // لون بني داكن أكثر للوضع الفاتح
                              letterSpacing: 0.2,
                              fontWeight:
                                  FontWeight
                                      .w500, // خط أكثر سمكاً لتحسين القراءة
                            ),
                            textDirection: TextDirection.rtl,
                            textAlign: TextAlign.right,
                            softWrap: true, // السماح بلف النص
                            overflow: TextOverflow.visible, // عدم قطع النص
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
