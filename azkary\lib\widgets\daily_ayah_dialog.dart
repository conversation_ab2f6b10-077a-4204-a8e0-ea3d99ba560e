import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart';
import '../services/daily_ayah_provider.dart';

/// نافذة منبثقة لعرض الآية اليومية
class DailyAyahDialog extends StatefulWidget {
  const DailyAyahDialog({super.key});

  @override
  State<DailyAyahDialog> createState() => _DailyAyahDialogState();
}

class _DailyAyahDialogState extends State<DailyAyahDialog>
    with SingleTickerProviderStateMixin {
  bool _showReadAnimation = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // عرض تأثير "تمت القراءة" وإغلاق النافذة بعد فترة
  void _showReadEffect() {
    setState(() {
      _showReadAnimation = true;
    });

    // إغلاق النافذة بعد انتهاء التأثير
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // ألوان التطبيق
    final primaryColor = theme.colorScheme.primary;
    final backgroundColor = isDarkMode ? const Color(0xFF1E2732) : Colors.white;
    final textColor = isDarkMode ? Colors.white : Colors.black87;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 8,
      backgroundColor: backgroundColor,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      child: Consumer<DailyAyahProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(color: primaryColor),
                  const SizedBox(height: 16),
                  Text(
                    'جاري تحميل الآية...',
                    style: TextStyle(color: textColor, fontSize: 16),
                  ),
                ],
              ),
            );
          }

          if (provider.error.isNotEmpty) {
            return Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    provider.error,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.red, fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => provider.loadDailyAyah(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (provider.dailyAyah == null) {
            return Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'لا توجد آية لليوم',
                    style: TextStyle(color: textColor, fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إغلاق'),
                  ),
                ],
              ),
            );
          }

          final dailyAyah = provider.dailyAyah!;

          // إذا كان يعرض تأثير تمت القراءة
          if (_showReadAnimation) {
            return Container(
              width: 300,
              height: 300,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Lottie.asset(
                      'assets/animations/quran_read_success.json',
                      controller: _animationController,
                      width: 200,
                      height: 200,
                      fit: BoxFit.contain,
                      onLoaded: (composition) {
                        // تشغيل التأثير عند تحميله
                        _animationController.forward();
                      },
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'بارك الله فيك',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          // عرض محتوى الآية العادي
          return Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            padding: const EdgeInsets.all(20),
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // عنوان النافذة
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'آية اليوم',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(
                        Icons.close,
                        color: textColor.withAlpha(153), // 0.6 * 255 = 153
                      ),
                      tooltip: 'إغلاق',
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // معلومات السورة والآية
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: primaryColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'سورة ${dailyAyah.surah.name}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: primaryColor,
                        ),
                      ),
                      Text(
                        'الآية ${dailyAyah.ayah.numberInSurah}',
                        style: TextStyle(fontSize: 14, color: primaryColor),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // نص الآية
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Text(
                          dailyAyah.ayah.text,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 22,
                            height: 2.0,
                            color: textColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),

                        if (provider.showTafsir &&
                            dailyAyah.tafsir != null) ...[
                          const SizedBox(height: 20),
                          const Divider(),
                          const SizedBox(height: 12),

                          // عنوان التفسير
                          Text(
                            'التفسير',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: primaryColor,
                            ),
                          ),

                          const SizedBox(height: 12),

                          // نص التفسير
                          Text(
                            dailyAyah.tafsir!,
                            textAlign: TextAlign.justify,
                            style: TextStyle(
                              fontSize: 14,
                              height: 1.8,
                              color: textColor.withAlpha(230),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // أزرار التنقل بين الآيات
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // زر الآية السابقة
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 4, right: 4),
                        child: ElevatedButton.icon(
                          onPressed: () => provider.previousAyah(),
                          icon: const Icon(Icons.arrow_back, size: 18),
                          label: const Text(
                            'السابقة',
                            style: TextStyle(fontSize: 13),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              vertical: 10,
                              horizontal: 8,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // زر الآية التالية
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 4, right: 4),
                        child: ElevatedButton.icon(
                          onPressed: () => provider.nextAyah(),
                          icon: const Icon(Icons.arrow_forward, size: 18),
                          label: const Text(
                            'التالية',
                            style: TextStyle(fontSize: 13),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              vertical: 10,
                              horizontal: 8,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // زر التفسير
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => provider.toggleTafsir(),
                    icon: Icon(
                      provider.showTafsir
                          ? Icons.visibility_off
                          : Icons.visibility,
                      size: 20,
                    ),
                    label: Text(
                      provider.showTafsir ? 'إخفاء التفسير' : 'عرض التفسير',
                      style: const TextStyle(fontSize: 14),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // زر تمت قراءتها
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () async {
                      // تحديث حالة قراءة الآية
                      final provider = Provider.of<DailyAyahProvider>(
                        context,
                        listen: false,
                      );
                      await provider.markAyahAsRead();

                      // عرض تأثير تمت القراءة
                      if (mounted) {
                        _showReadEffect();
                      }
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: primaryColor,
                      side: BorderSide(color: primaryColor),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'تمت قراءتها',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
